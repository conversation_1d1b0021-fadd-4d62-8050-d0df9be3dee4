{% extends "base.html" %}

{% block title %}Importar Dados - Torneio BAGREMON{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <h2 class="text-4xl font-bold text-white mb-2">
            <i class="fas fa-file-import text-green-400 mr-3"></i>
            Importar e Exportar Dados
        </h2>
        <p class="text-white/80 text-lg">Gerencie dados do torneio em lote</p>
    </div>

    <!-- Seção de Importação -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg p-6">
        <h3 class="text-xl font-semibold text-white mb-4">
            <i class="fas fa-upload mr-2"></i>
            Importar Jogadores
        </h3>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Formulário de Upload -->
            <div>
                <div class="mb-4">
                    <label class="block text-white font-medium mb-2">Selecionar Arquivo</label>
                    <input type="file" id="arquivo-importacao" accept=".csv,.xlsx,.xls"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500">
                    <p class="text-white/60 text-sm mt-1">Formatos aceitos: CSV, Excel (.xlsx, .xls)</p>
                </div>
                
                <button onclick="importarJogadores()" 
                        class="w-full bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
                    <i class="fas fa-upload mr-2"></i>
                    Importar Jogadores
                </button>
                
                <div id="resultado-importacao" class="mt-4 hidden">
                    <!-- Resultado da importação aparecerá aqui -->
                </div>
            </div>
            
            <!-- Instruções e Modelo -->
            <div>
                <h4 class="text-white font-semibold mb-3">Formato do Arquivo</h4>
                <div class="bg-black/20 rounded-lg p-4 mb-4">
                    <p class="text-white/80 text-sm mb-2">Colunas obrigatórias:</p>
                    <ul class="text-white/60 text-sm space-y-1">
                        <li>• <strong>Nome Real</strong> - Nome completo do jogador</li>
                        <li>• <strong>Nickname</strong> - Apelido único do jogador</li>
                        <li>• <strong>Tipo Primário</strong> - Tipo principal (Fire, Water, etc.)</li>
                    </ul>
                    
                    <p class="text-white/80 text-sm mt-3 mb-2">Colunas opcionais:</p>
                    <ul class="text-white/60 text-sm space-y-1">
                        <li>• <strong>Tipo Secundário</strong> - Tipo secundário</li>
                    </ul>
                </div>
                
                <button onclick="baixarModelo()" 
                        class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg text-sm">
                    <i class="fas fa-download mr-2"></i>
                    Baixar Modelo CSV
                </button>
            </div>
        </div>
    </div>

    <!-- Seção de Exportação -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg p-6">
        <h3 class="text-xl font-semibold text-white mb-4">
            <i class="fas fa-download mr-2"></i>
            Exportar Dados
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <!-- Exportar Jogadores -->
            <div class="bg-white/5 rounded-lg p-4 text-center">
                <i class="fas fa-users text-blue-400 text-2xl mb-3"></i>
                <h4 class="text-white font-medium mb-2">Jogadores</h4>
                <p class="text-white/60 text-sm mb-3">Lista completa com estatísticas</p>
                <button onclick="exportarJogadores()" 
                        class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 rounded text-sm">
                    <i class="fas fa-download mr-1"></i>
                    CSV
                </button>
            </div>
            
            <!-- Exportar Batalhas -->
            <div class="bg-white/5 rounded-lg p-4 text-center">
                <i class="fas fa-fist-raised text-red-400 text-2xl mb-3"></i>
                <h4 class="text-white font-medium mb-2">Batalhas</h4>
                <p class="text-white/60 text-sm mb-3">Histórico completo de batalhas</p>
                <button onclick="exportarBatalhas()" 
                        class="w-full bg-red-500 hover:bg-red-600 text-white py-2 px-3 rounded text-sm">
                    <i class="fas fa-download mr-1"></i>
                    CSV
                </button>
            </div>
            
            <!-- Exportar Classificação -->
            <div class="bg-white/5 rounded-lg p-4 text-center">
                <i class="fas fa-trophy text-yellow-400 text-2xl mb-3"></i>
                <h4 class="text-white font-medium mb-2">Classificação</h4>
                <p class="text-white/60 text-sm mb-3">Ranking atual do torneio</p>
                <button onclick="exportarClassificacao()" 
                        class="w-full bg-yellow-500 hover:bg-yellow-600 text-white py-2 px-3 rounded text-sm">
                    <i class="fas fa-download mr-1"></i>
                    CSV
                </button>
            </div>
            
            <!-- Backup Completo -->
            <div class="bg-white/5 rounded-lg p-4 text-center">
                <i class="fas fa-database text-purple-400 text-2xl mb-3"></i>
                <h4 class="text-white font-medium mb-2">Backup</h4>
                <p class="text-white/60 text-sm mb-3">Dados completos em JSON</p>
                <button onclick="exportarBackup()" 
                        class="w-full bg-purple-500 hover:bg-purple-600 text-white py-2 px-3 rounded text-sm">
                    <i class="fas fa-download mr-1"></i>
                    JSON
                </button>
            </div>
        </div>
    </div>

    <!-- Seção de Backup e Restauração -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg p-6">
        <h3 class="text-xl font-semibold text-white mb-4">
            <i class="fas fa-shield-alt mr-2"></i>
            Backup e Restauração
        </h3>
        
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Criar Backup -->
            <div>
                <h4 class="text-white font-medium mb-3">Criar Backup Completo</h4>
                <p class="text-white/60 text-sm mb-4">
                    Salve todos os dados do torneio em um arquivo JSON que pode ser usado para restaurar 
                    ou transferir para outro computador.
                </p>
                <button onclick="criarBackupCompleto()" 
                        class="w-full bg-green-500 hover:bg-green-600 text-white py-3 px-4 rounded-lg">
                    <i class="fas fa-save mr-2"></i>
                    Criar Backup Completo
                </button>
            </div>
            
            <!-- Restaurar Backup -->
            <div>
                <h4 class="text-white font-medium mb-3">Restaurar Backup</h4>
                <p class="text-white/60 text-sm mb-4">
                    Carregue um arquivo de backup para restaurar dados de um torneio anterior.
                    <strong class="text-red-400">Atenção: Isso substituirá todos os dados atuais!</strong>
                </p>
                <input type="file" id="arquivo-backup" accept=".json"
                       class="w-full px-3 py-2 border border-gray-300 rounded-md mb-3">
                <button onclick="restaurarBackup()" 
                        class="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 px-4 rounded-lg">
                    <i class="fas fa-upload mr-2"></i>
                    Restaurar Backup
                </button>
            </div>
        </div>
    </div>

    <!-- Histórico de Operações -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg p-6">
        <h3 class="text-xl font-semibold text-white mb-4">
            <i class="fas fa-history mr-2"></i>
            Histórico de Operações
        </h3>
        
        <div id="historico-operacoes" class="space-y-2">
            <p class="text-white/60 text-center py-4">Nenhuma operação realizada ainda</p>
        </div>
    </div>

    <!-- Botão Voltar -->
    <div class="text-center">
        <a href="/" 
           class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition-colors inline-block">
            <i class="fas fa-arrow-left mr-2"></i>
            Voltar ao Início
        </a>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let historicoOperacoes = [];

// Função para importar jogadores
async function importarJogadores() {
    const arquivo = document.getElementById('arquivo-importacao').files[0];
    
    if (!arquivo) {
        mostrarNotificacao('Selecione um arquivo para importar.', 'warning');
        return;
    }
    
    const formData = new FormData();
    formData.append('arquivo', arquivo);
    
    try {
        const response = await fetch('/api/importar-jogadores', {
            method: 'POST',
            body: formData
        });
        
        const result = await response.json();
        
        if (result.sucesso) {
            mostrarResultadoImportacao(result);
            adicionarHistorico('Importação de jogadores', `${result.jogadores_criados} jogadores importados`);
        } else {
            mostrarNotificacao('Erro na importação: ' + result.erro, 'error');
        }
        
    } catch (error) {
        console.error('Erro na importação:', error);
        mostrarNotificacao('Erro ao importar arquivo.', 'error');
    }
}

// Função para mostrar resultado da importação
function mostrarResultadoImportacao(result) {
    const container = document.getElementById('resultado-importacao');
    container.classList.remove('hidden');
    
    let html = `
        <div class="bg-green-500/20 border border-green-400 rounded-lg p-4">
            <h4 class="text-green-400 font-semibold mb-2">
                <i class="fas fa-check-circle mr-2"></i>
                Importação Concluída
            </h4>
            <p class="text-white">
                <strong>${result.jogadores_criados}</strong> jogadores importados com sucesso.
            </p>
    `;
    
    if (result.erros && result.erros.length > 0) {
        html += `
            <div class="mt-3">
                <h5 class="text-yellow-400 font-medium mb-2">Avisos:</h5>
                <ul class="text-white/80 text-sm space-y-1">
        `;
        
        result.erros.forEach(erro => {
            html += `<li>• ${erro}</li>`;
        });
        
        html += `
                </ul>
            </div>
        `;
    }
    
    html += '</div>';
    container.innerHTML = html;
}

// Função para baixar modelo CSV
function baixarModelo() {
    const csvContent = `Nome Real,Nickname,Tipo Primário,Tipo Secundário
João Silva,DragonMaster,Dragon,Fire
Maria Santos,AquaQueen,Water,Ice
Pedro Costa,ThunderBolt,Electric,`;
    
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    link.href = URL.createObjectURL(blob);
    link.download = 'modelo_jogadores.csv';
    link.click();
    
    adicionarHistorico('Download', 'Modelo CSV baixado');
}

// Funções de exportação
async function exportarJogadores() {
    try {
        const response = await fetch('/api/exportar-jogadores');
        const result = await response.json();
        
        if (result.sucesso) {
            window.open(result.arquivo, '_blank');
            adicionarHistorico('Exportação', 'Dados de jogadores exportados');
            mostrarNotificacao('Dados exportados com sucesso!', 'success');
        }
    } catch (error) {
        mostrarNotificacao('Erro ao exportar dados.', 'error');
    }
}

async function exportarBatalhas() {
    // Implementar exportação de batalhas
    mostrarNotificacao('Funcionalidade em desenvolvimento!', 'info');
}

async function exportarClassificacao() {
    // Implementar exportação de classificação
    mostrarNotificacao('Funcionalidade em desenvolvimento!', 'info');
}

async function exportarBackup() {
    // Implementar exportação de backup
    mostrarNotificacao('Funcionalidade em desenvolvimento!', 'info');
}

// Funções de backup
async function criarBackupCompleto() {
    try {
        // Buscar todos os dados
        const [jogadores, batalhas] = await Promise.all([
            fetch('/api/jogadores').then(r => r.json()),
            fetch('/api/batalhas').then(r => r.json())
        ]);
        
        const backup = {
            versao: '1.0',
            data_backup: new Date().toISOString(),
            torneio: 'BAGREMON',
            dados: {
                jogadores,
                batalhas
            }
        };
        
        const blob = new Blob([JSON.stringify(backup, null, 2)], { type: 'application/json' });
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = `backup_bagremon_${new Date().toISOString().split('T')[0]}.json`;
        link.click();
        
        adicionarHistorico('Backup', 'Backup completo criado');
        mostrarNotificacao('Backup criado com sucesso!', 'success');
        
    } catch (error) {
        console.error('Erro ao criar backup:', error);
        mostrarNotificacao('Erro ao criar backup.', 'error');
    }
}

async function restaurarBackup() {
    const arquivo = document.getElementById('arquivo-backup').files[0];
    
    if (!arquivo) {
        mostrarNotificacao('Selecione um arquivo de backup.', 'warning');
        return;
    }
    
    if (!confirm('ATENÇÃO: Esta operação substituirá todos os dados atuais. Deseja continuar?')) {
        return;
    }
    
    try {
        const texto = await arquivo.text();
        const backup = JSON.parse(texto);
        
        // Validar estrutura do backup
        if (!backup.dados || !backup.dados.jogadores) {
            throw new Error('Arquivo de backup inválido');
        }
        
        // Implementar restauração (seria necessário endpoint específico)
        mostrarNotificacao('Funcionalidade de restauração em desenvolvimento!', 'info');
        
    } catch (error) {
        console.error('Erro ao restaurar backup:', error);
        mostrarNotificacao('Erro ao restaurar backup: ' + error.message, 'error');
    }
}

// Função para adicionar ao histórico
function adicionarHistorico(tipo, descricao) {
    const agora = new Date().toLocaleString('pt-BR');
    historicoOperacoes.unshift({ tipo, descricao, data: agora });
    
    // Manter apenas os últimos 10
    if (historicoOperacoes.length > 10) {
        historicoOperacoes = historicoOperacoes.slice(0, 10);
    }
    
    atualizarHistorico();
}

// Função para atualizar exibição do histórico
function atualizarHistorico() {
    const container = document.getElementById('historico-operacoes');
    
    if (historicoOperacoes.length === 0) {
        container.innerHTML = '<p class="text-white/60 text-center py-4">Nenhuma operação realizada ainda</p>';
        return;
    }
    
    container.innerHTML = '';
    
    historicoOperacoes.forEach(operacao => {
        const div = document.createElement('div');
        div.className = 'flex justify-between items-center p-3 bg-white/5 rounded-lg';
        div.innerHTML = `
            <div>
                <span class="text-white font-medium">${operacao.tipo}</span>
                <span class="text-white/60 ml-2">${operacao.descricao}</span>
            </div>
            <span class="text-white/60 text-sm">${operacao.data}</span>
        `;
        container.appendChild(div);
    });
}

// Carregar histórico do localStorage ao iniciar
document.addEventListener('DOMContentLoaded', () => {
    const historico = localStorage.getItem('historico_operacoes');
    if (historico) {
        historicoOperacoes = JSON.parse(historico);
        atualizarHistorico();
    }
});

// Salvar histórico no localStorage
function salvarHistorico() {
    localStorage.setItem('historico_operacoes', JSON.stringify(historicoOperacoes));
}

// Salvar histórico sempre que houver mudança
const originalAdicionarHistorico = adicionarHistorico;
adicionarHistorico = function(tipo, descricao) {
    originalAdicionarHistorico(tipo, descricao);
    salvarHistorico();
};
</script>
{% endblock %}
