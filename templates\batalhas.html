{% extends "base.html" %}

{% block title %}Batalhas - Campeonato Pokémon VGC{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-4xl font-bold text-white mb-2">
                <i class="fas fa-fist-raised text-red-400 mr-3"></i>
                Registro de Batalhas
            </h2>
            <p class="text-white/80 text-lg">Registre os resultados das batalhas VGC 2x2</p>
        </div>
        
        <button onclick="abrirModalBatalha()" 
                class="bg-red-500 hover:bg-red-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
            <i class="fas fa-plus mr-2"></i>
            Nova Batalha
        </button>
    </div>

    <!-- Estatísticas das Batalhas -->
    <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="total-batalhas-count">0</div>
            <div class="text-white/70 text-sm">Total de Batalhas</div>
        </div>
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="batalhas-hoje">0</div>
            <div class="text-white/70 text-sm">Batalhas Hoje</div>
        </div>
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="pokemon-mais-usado">-</div>
            <div class="text-white/70 text-sm">Pokémon Mais Usado</div>
        </div>
    </div>

    <!-- Histórico de Batalhas -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-white/20">
            <h3 class="text-xl font-semibold text-white">
                <i class="fas fa-history mr-2"></i>
                Histórico de Batalhas
            </h3>
        </div>
        
        <div class="p-6">
            <div id="lista-batalhas" class="space-y-4">
                <!-- Será preenchido via JavaScript -->
            </div>
        </div>
    </div>
</div>

<!-- Modal para Nova Batalha -->
<div id="modal-batalha" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-4xl w-full p-6 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-6">
                <h3 class="text-2xl font-bold text-gray-800">
                    <i class="fas fa-fist-raised text-red-500 mr-2"></i>
                    Registrar Nova Batalha
                </h3>
                <button onclick="fecharModalBatalha()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times text-xl"></i>
                </button>
            </div>
            
            <form id="form-batalha" class="space-y-6">
                <!-- Seleção dos Jogadores -->
                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <!-- Jogador A -->
                    <div class="border-2 border-blue-200 rounded-lg p-4">
                        <h4 class="text-lg font-semibold text-blue-600 mb-3">
                            <i class="fas fa-user mr-2"></i>
                            Jogador A
                        </h4>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Selecionar Jogador</label>
                            <select id="player-a" required onchange="carregarPokemonsJogador('A')"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                <option value="">Selecione o jogador...</option>
                                {% for jogador in jogadores %}
                                <option value="{{ jogador.id }}">{{ jogador.nickname }} ({{ jogador.nome_real }})</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Pokémon 1</label>
                                <select id="pokemon-a1" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">Selecione...</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Pokémon 2</label>
                                <select id="pokemon-a2" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                                    <option value="">Selecione...</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    
                    <!-- Jogador B -->
                    <div class="border-2 border-red-200 rounded-lg p-4">
                        <h4 class="text-lg font-semibold text-red-600 mb-3">
                            <i class="fas fa-user mr-2"></i>
                            Jogador B
                        </h4>
                        
                        <div class="mb-4">
                            <label class="block text-sm font-medium text-gray-700 mb-2">Selecionar Jogador</label>
                            <select id="player-b" required onchange="carregarPokemonsJogador('B')"
                                    class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                                <option value="">Selecione o jogador...</option>
                                {% for jogador in jogadores %}
                                <option value="{{ jogador.id }}">{{ jogador.nickname }} ({{ jogador.nome_real }})</option>
                                {% endfor %}
                            </select>
                        </div>
                        
                        <div class="grid grid-cols-2 gap-3">
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Pokémon 1</label>
                                <select id="pokemon-b1" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                                    <option value="">Selecione...</option>
                                </select>
                            </div>
                            <div>
                                <label class="block text-sm font-medium text-gray-700 mb-2">Pokémon 2</label>
                                <select id="pokemon-b2" required
                                        class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-red-500">
                                    <option value="">Selecione...</option>
                                </select>
                            </div>
                        </div>
                    </div>
                </div>
                
                <!-- Resultado da Batalha -->
                <div class="border-2 border-yellow-200 rounded-lg p-4">
                    <h4 class="text-lg font-semibold text-yellow-600 mb-3">
                        <i class="fas fa-trophy mr-2"></i>
                        Resultado da Batalha
                    </h4>
                    
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Vencedor</label>
                            <div class="space-y-2">
                                <label class="flex items-center">
                                    <input type="radio" name="vencedor" value="A" required class="mr-2">
                                    <span class="text-blue-600 font-medium">Jogador A</span>
                                </label>
                                <label class="flex items-center">
                                    <input type="radio" name="vencedor" value="B" required class="mr-2">
                                    <span class="text-red-600 font-medium">Jogador B</span>
                                </label>
                            </div>
                        </div>
                        
                        <div>
                            <label class="block text-sm font-medium text-gray-700 mb-2">Pokémon Destaque</label>
                            <input type="text" id="pokemon-destaque" 
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500"
                                   placeholder="Nome do Pokémon que se destacou na batalha">
                        </div>
                    </div>
                    
                    <div class="mt-4">
                        <label class="block text-sm font-medium text-gray-700 mb-2">Fase do Campeonato</label>
                        <select id="fase-batalha"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-yellow-500">
                            <option value="classificatoria">Fase Classificatória</option>
                            <option value="repescagem">Repescagem</option>
                            <option value="semifinal">Semifinal</option>
                            <option value="final">Final</option>
                        </select>
                    </div>
                </div>
                
                <!-- Botões -->
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="fecharModalBatalha()"
                            class="px-6 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                        Cancelar
                    </button>
                    <button type="submit"
                            class="px-6 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">
                        <i class="fas fa-save mr-2"></i>
                        Registrar Batalha
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let pokemonsJogadores = {};

// Função para carregar batalhas
async function carregarBatalhas() {
    try {
        const response = await fetch('/api/batalhas');
        const batalhas = await response.json();
        
        const lista = document.getElementById('lista-batalhas');
        lista.innerHTML = '';
        
        // Atualizar estatísticas
        document.getElementById('total-batalhas-count').textContent = batalhas.length;
        
        // Contar batalhas de hoje
        const hoje = new Date().toDateString();
        const batalhasHoje = batalhas.filter(b => 
            new Date(b.created_at).toDateString() === hoje
        ).length;
        document.getElementById('batalhas-hoje').textContent = batalhasHoje;
        
        // Encontrar Pokémon mais usado
        const pokemonCount = {};
        batalhas.forEach(b => {
            [b.pokemon_a1, b.pokemon_a2, b.pokemon_b1, b.pokemon_b2].forEach(pokemon => {
                if (pokemon) {
                    pokemonCount[pokemon] = (pokemonCount[pokemon] || 0) + 1;
                }
            });
        });
        
        if (Object.keys(pokemonCount).length > 0) {
            const pokemonMaisUsado = Object.keys(pokemonCount).reduce((a, b) => 
                pokemonCount[a] > pokemonCount[b] ? a : b
            );
            document.getElementById('pokemon-mais-usado').textContent = pokemonMaisUsado;
        }
        
        if (batalhas.length === 0) {
            lista.innerHTML = '<p class="text-white/60 text-center py-8">Nenhuma batalha registrada ainda</p>';
            return;
        }
        
        batalhas.forEach(batalha => {
            const card = document.createElement('div');
            card.className = 'bg-white/5 rounded-lg p-4 border border-white/10';
            
            const dataFormatada = new Date(batalha.created_at).toLocaleString('pt-BR');
            const vencedorNome = batalha.vencedor === 'A' ? batalha.jogador_a.nickname : batalha.jogador_b.nickname;
            
            card.innerHTML = `
                <div class="flex justify-between items-start mb-3">
                    <div class="flex items-center space-x-4">
                        <span class="bg-${batalha.fase === 'classificatoria' ? 'blue' : batalha.fase === 'final' ? 'yellow' : 'purple'}-500 text-white px-2 py-1 rounded text-xs font-medium">
                            ${batalha.fase.charAt(0).toUpperCase() + batalha.fase.slice(1)}
                        </span>
                        <span class="text-white/60 text-sm">${dataFormatada}</span>
                    </div>
                    <span class="text-green-400 font-bold">
                        <i class="fas fa-trophy mr-1"></i>
                        ${vencedorNome}
                    </span>
                </div>
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <!-- Jogador A -->
                    <div class="border-l-4 border-blue-400 pl-4">
                        <div class="text-white font-medium mb-2">
                            ${batalha.jogador_a.nickname}
                            ${batalha.vencedor === 'A' ? '<i class="fas fa-crown text-yellow-400 ml-2"></i>' : ''}
                        </div>
                        <div class="text-white/70 text-sm space-y-1">
                            <div>• ${batalha.pokemon_a1}</div>
                            <div>• ${batalha.pokemon_a2}</div>
                        </div>
                    </div>
                    
                    <!-- VS -->
                    <div class="flex items-center justify-center md:hidden">
                        <span class="text-white/60 font-bold">VS</span>
                    </div>
                    
                    <!-- Jogador B -->
                    <div class="border-l-4 border-red-400 pl-4">
                        <div class="text-white font-medium mb-2">
                            ${batalha.jogador_b.nickname}
                            ${batalha.vencedor === 'B' ? '<i class="fas fa-crown text-yellow-400 ml-2"></i>' : ''}
                        </div>
                        <div class="text-white/70 text-sm space-y-1">
                            <div>• ${batalha.pokemon_b1}</div>
                            <div>• ${batalha.pokemon_b2}</div>
                        </div>
                    </div>
                </div>
                
                ${batalha.pokemon_destaque ? `
                <div class="mt-3 pt-3 border-t border-white/10">
                    <span class="text-yellow-400 text-sm">
                        <i class="fas fa-star mr-1"></i>
                        Pokémon Destaque: ${batalha.pokemon_destaque}
                    </span>
                </div>
                ` : ''}
            `;
            
            lista.appendChild(card);
        });
        
    } catch (error) {
        console.error('Erro ao carregar batalhas:', error);
        alert('Erro ao carregar batalhas. Tente novamente.');
    }
}

// Função para carregar Pokémon de um jogador
async function carregarPokemonsJogador(jogador) {
    const jogadorId = document.getElementById(`player-${jogador.toLowerCase()}`).value;
    
    if (!jogadorId) {
        // Limpar selects de Pokémon
        document.getElementById(`pokemon-${jogador.toLowerCase()}1`).innerHTML = '<option value="">Selecione...</option>';
        document.getElementById(`pokemon-${jogador.toLowerCase()}2`).innerHTML = '<option value="">Selecione...</option>';
        return;
    }
    
    try {
        const response = await fetch(`/api/pokemons?jogador_id=${jogadorId}`);
        const pokemons = await response.json();
        
        pokemonsJogadores[jogador] = pokemons;
        
        const select1 = document.getElementById(`pokemon-${jogador.toLowerCase()}1`);
        const select2 = document.getElementById(`pokemon-${jogador.toLowerCase()}2`);
        
        // Limpar e preencher selects
        select1.innerHTML = '<option value="">Selecione...</option>';
        select2.innerHTML = '<option value="">Selecione...</option>';
        
        pokemons.forEach(pokemon => {
            const optionText = pokemon.apelido ? `${pokemon.nome} "${pokemon.apelido}"` : pokemon.nome;
            const option1 = new Option(optionText, pokemon.nome);
            const option2 = new Option(optionText, pokemon.nome);
            
            select1.add(option1);
            select2.add(option2);
        });
        
    } catch (error) {
        console.error('Erro ao carregar Pokémon:', error);
        alert('Erro ao carregar Pokémon do jogador.');
    }
}

// Função para abrir modal de batalha
function abrirModalBatalha() {
    document.getElementById('modal-batalha').classList.remove('hidden');
    document.getElementById('form-batalha').reset();
    
    // Limpar selects de Pokémon
    ['a1', 'a2', 'b1', 'b2'].forEach(id => {
        document.getElementById(`pokemon-${id}`).innerHTML = '<option value="">Selecione...</option>';
    });
}

// Função para fechar modal de batalha
function fecharModalBatalha() {
    document.getElementById('modal-batalha').classList.add('hidden');
}

// Event listener para o formulário de batalha
document.getElementById('form-batalha').addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const data = {
        player_a_id: parseInt(document.getElementById('player-a').value),
        player_b_id: parseInt(document.getElementById('player-b').value),
        pokemon_a1: document.getElementById('pokemon-a1').value,
        pokemon_a2: document.getElementById('pokemon-a2').value,
        pokemon_b1: document.getElementById('pokemon-b1').value,
        pokemon_b2: document.getElementById('pokemon-b2').value,
        vencedor: document.querySelector('input[name="vencedor"]:checked').value,
        pokemon_destaque: document.getElementById('pokemon-destaque').value || null,
        fase: document.getElementById('fase-batalha').value
    };
    
    // Validar se os jogadores são diferentes
    if (data.player_a_id === data.player_b_id) {
        alert('Os jogadores devem ser diferentes!');
        return;
    }
    
    // Validar se os Pokémon do mesmo jogador são diferentes
    if (data.pokemon_a1 === data.pokemon_a2) {
        alert('O Jogador A deve usar Pokémon diferentes!');
        return;
    }
    
    if (data.pokemon_b1 === data.pokemon_b2) {
        alert('O Jogador B deve usar Pokémon diferentes!');
        return;
    }
    
    try {
        const response = await fetch('/api/batalhas', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            alert('Batalha registrada com sucesso!');
            fecharModalBatalha();
            carregarBatalhas();
        } else {
            const error = await response.json();
            alert(error.error || 'Erro ao registrar batalha.');
        }
    } catch (error) {
        console.error('Erro ao registrar batalha:', error);
        alert('Erro ao registrar batalha.');
    }
});

// Carregar batalhas ao carregar a página
document.addEventListener('DOMContentLoaded', carregarBatalhas);
</script>
{% endblock %}
