{% extends "base.html" %}

{% block title %}Classificação - Campeonato Pokémon VGC{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header da Classificação -->
    <div class="text-center">
        <h2 class="text-4xl font-bold text-white mb-2">
            <i class="fas fa-trophy text-yellow-400 mr-3"></i>
            Classificação Geral
        </h2>
        <p class="text-white/80 text-lg">Acompanhe a classificação em tempo real do campeonato</p>
    </div>

    <!-- Estatísticas Gerais -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="total-jogadores">{{ jogadores|length }}</div>
            <div class="text-white/70 text-sm">Jo<PERSON><PERSON></div>
        </div>
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="total-batalhas">0</div>
            <div class="text-white/70 text-sm">Batalhas</div>
        </div>
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="lider-nome">-</div>
            <div class="text-white/70 text-sm">Líder Atual</div>
        </div>
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="pokemon-destaque">-</div>
            <div class="text-white/70 text-sm">Pokémon Destaque</div>
        </div>
    </div>

    <!-- Tabela de Classificação -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-white/20">
            <h3 class="text-xl font-semibold text-white">
                <i class="fas fa-list-ol mr-2"></i>
                Tabela de Classificação
            </h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-black/20">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-white/80 uppercase tracking-wider">Pos</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-white/80 uppercase tracking-wider">Jogador</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-white/80 uppercase tracking-wider">Tipos</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-white/80 uppercase tracking-wider">V</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-white/80 uppercase tracking-wider">D</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-white/80 uppercase tracking-wider">Pts</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-white/80 uppercase tracking-wider">Pokémon Destaque</th>
                    </tr>
                </thead>
                <tbody id="tabela-classificacao" class="divide-y divide-white/10">
                    <!-- Será preenchido via JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Cards dos Jogadores (versão mobile-friendly) -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4 mt-8" id="cards-jogadores">
        <!-- Será preenchido via JavaScript -->
    </div>

    <!-- Botão para atualizar -->
    <div class="text-center">
        <button onclick="carregarClassificacao()" 
                class="bg-yellow-500 hover:bg-yellow-600 text-black font-bold py-3 px-6 rounded-lg transition-colors">
            <i class="fas fa-sync-alt mr-2"></i>
            Atualizar Classificação
        </button>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Função para obter a classe CSS do tipo Pokémon
function getTipoClass(tipo) {
    return 'tipo-' + tipo.toLowerCase();
}

// Função para obter a classe da posição
function getPosicaoClass(posicao, totalJogadores) {
    if (posicao === 1) return 'posicao-1';
    if (posicao === totalJogadores) return 'posicao-ultimo';
    return 'posicao-normal';
}

// Função para carregar e exibir a classificação
async function carregarClassificacao() {
    try {
        const response = await fetch('/api/classificacao');
        const jogadores = await response.json();
        
        const tabela = document.getElementById('tabela-classificacao');
        const cards = document.getElementById('cards-jogadores');
        
        // Limpar conteúdo anterior
        tabela.innerHTML = '';
        cards.innerHTML = '';
        
        // Atualizar estatísticas gerais
        if (jogadores.length > 0) {
            document.getElementById('lider-nome').textContent = jogadores[0].nickname;
            
            // Encontrar Pokémon mais destacado
            const pokemonCount = {};
            jogadores.forEach(j => {
                if (j.pokemon_destaque) {
                    pokemonCount[j.pokemon_destaque] = (pokemonCount[j.pokemon_destaque] || 0) + 1;
                }
            });
            
            if (Object.keys(pokemonCount).length > 0) {
                const pokemonDestaque = Object.keys(pokemonCount).reduce((a, b) => 
                    pokemonCount[a] > pokemonCount[b] ? a : b
                );
                document.getElementById('pokemon-destaque').textContent = pokemonDestaque;
            }
        }
        
        // Preencher tabela e cards
        jogadores.forEach((jogador, index) => {
            const posicao = index + 1;
            const posicaoClass = getPosicaoClass(posicao, jogadores.length);
            
            // Linha da tabela
            const row = document.createElement('tr');
            row.className = 'hover:bg-white/5 transition-colors';
            row.innerHTML = `
                <td class="px-6 py-4">
                    <span class="inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold ${posicaoClass}">
                        ${posicao}
                    </span>
                </td>
                <td class="px-6 py-4">
                    <div class="flex items-center">
                        <div class="flex-shrink-0 h-10 w-10">
                            <img class="h-10 w-10 rounded-full object-cover border-2 border-white/20" 
                                 src="${jogador.foto_perfil || '/static/uploads/default-avatar.png'}" 
                                 alt="${jogador.nickname}">
                        </div>
                        <div class="ml-4">
                            <div class="text-sm font-medium text-white">${jogador.nickname}</div>
                            <div class="text-sm text-white/60">${jogador.nome_real}</div>
                        </div>
                    </div>
                </td>
                <td class="px-6 py-4">
                    <div class="flex space-x-1">
                        <span class="tipo-badge ${getTipoClass(jogador.tipo_primario)}">${jogador.tipo_primario}</span>
                        ${jogador.tipo_secundario ? `<span class="tipo-badge ${getTipoClass(jogador.tipo_secundario)}">${jogador.tipo_secundario}</span>` : ''}
                    </div>
                </td>
                <td class="px-6 py-4 text-center text-sm text-white font-medium">${jogador.vitorias}</td>
                <td class="px-6 py-4 text-center text-sm text-white font-medium">${jogador.derrotas}</td>
                <td class="px-6 py-4 text-center text-sm text-white font-bold">${jogador.pontuacao}</td>
                <td class="px-6 py-4 text-sm text-white">${jogador.pokemon_destaque || '-'}</td>
            `;
            tabela.appendChild(row);
            
            // Card do jogador
            const card = document.createElement('div');
            card.className = `bg-white/10 backdrop-blur-md rounded-lg p-4 card-hover cursor-pointer ${posicaoClass === 'posicao-1' ? 'ring-2 ring-yellow-400' : ''}`;
            card.innerHTML = `
                <div class="flex items-center justify-between mb-3">
                    <span class="inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold ${posicaoClass}">
                        ${posicao}
                    </span>
                    <div class="flex space-x-1">
                        <span class="tipo-badge ${getTipoClass(jogador.tipo_primario)}">${jogador.tipo_primario}</span>
                        ${jogador.tipo_secundario ? `<span class="tipo-badge ${getTipoClass(jogador.tipo_secundario)}">${jogador.tipo_secundario}</span>` : ''}
                    </div>
                </div>
                
                <div class="text-center mb-3">
                    <img class="h-16 w-16 rounded-full mx-auto object-cover border-2 border-white/20 mb-2" 
                         src="${jogador.foto_perfil || '/static/uploads/default-avatar.png'}" 
                         alt="${jogador.nickname}">
                    <h3 class="text-white font-bold">${jogador.nickname}</h3>
                    <p class="text-white/60 text-sm">${jogador.nome_real}</p>
                </div>
                
                <div class="grid grid-cols-3 gap-2 text-center text-sm">
                    <div>
                        <div class="text-green-400 font-bold">${jogador.vitorias}</div>
                        <div class="text-white/60">V</div>
                    </div>
                    <div>
                        <div class="text-red-400 font-bold">${jogador.derrotas}</div>
                        <div class="text-white/60">D</div>
                    </div>
                    <div>
                        <div class="text-yellow-400 font-bold">${jogador.pontuacao}</div>
                        <div class="text-white/60">Pts</div>
                    </div>
                </div>
                
                ${jogador.pokemon_destaque ? `
                <div class="mt-3 text-center">
                    <div class="text-white/60 text-xs">Pokémon Destaque</div>
                    <div class="text-white text-sm font-medium">${jogador.pokemon_destaque}</div>
                </div>
                ` : ''}
            `;
            
            // Adicionar evento de clique para ver detalhes
            card.addEventListener('click', () => {
                // Implementar modal ou página de detalhes do jogador
                console.log('Ver detalhes do jogador:', jogador);
            });
            
            cards.appendChild(card);
        });
        
    } catch (error) {
        console.error('Erro ao carregar classificação:', error);
        alert('Erro ao carregar classificação. Tente novamente.');
    }
}

// Carregar classificação ao carregar a página
document.addEventListener('DOMContentLoaded', carregarClassificacao);

// Atualizar classificação a cada 30 segundos
setInterval(carregarClassificacao, 30000);
</script>
{% endblock %}
