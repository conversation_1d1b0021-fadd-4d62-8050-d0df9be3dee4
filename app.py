from flask import Flask, render_template, request, jsonify, redirect, url_for, flash, send_from_directory
from flask_cors import CORS
from werkzeug.utils import secure_filename
import os
import shutil
from models import db, <PERSON><PERSON><PERSON>, <PERSON>ke<PERSON>, Batal<PERSON>, Repescagem
from datetime import datetime, date

app = Flask(__name__)
app.config['SECRET_KEY'] = 'bagremon_tournament_2025'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///torneio_bagremon.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Configurar CORS
CORS(app)

# Inicializar banco de dados
db.init_app(app)

# Criar pastas necessárias
os.makedirs('static/uploads/jogadores', exist_ok=True)
os.makedirs('static/uploads/pokemons', exist_ok=True)
os.makedirs('static/imagens', exist_ok=True)
os.makedirs('static/background', exist_ok=True)

# Datas especiais do torneio
DATA_AMISTOSO = date(2025, 7, 5)
DATA_OFICIAL = date(2025, 7, 12)

# Tipos Pokémon válidos
TIPOS_POKEMON = [
    'Normal', 'Fire', 'Water', 'Electric', 'Grass', 'Ice',
    'Fighting', 'Poison', 'Ground', 'Flying', 'Psychic', 'Bug',
    'Rock', 'Ghost', 'Dragon', 'Dark', 'Steel', 'Fairy'
]

def detectar_modo_torneio():
    """Detecta o modo do torneio baseado na data atual"""
    hoje = date.today()

    if hoje == DATA_AMISTOSO:
        return 'amistoso'
    elif hoje == DATA_OFICIAL:
        return 'oficial'
    else:
        return 'selecionar'  # Usuário deve escolher

def salvar_imagem_jogador(arquivo):
    """Salva imagem do jogador na pasta imagens/"""
    if arquivo and arquivo.filename:
        filename = secure_filename(arquivo.filename)
        # Gerar nome único baseado no timestamp
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        nome_arquivo = f"jogador_{timestamp}_{filename}"
        caminho = os.path.join('static/imagens', nome_arquivo)
        arquivo.save(caminho)
        return f"/static/imagens/{nome_arquivo}"
    return None

@app.route('/')
def index():
    """Página principal com detecção automática do modo"""
    modo = detectar_modo_torneio()
    jogadores = Jogador.query.order_by(Jogador.pontuacao.desc(), Jogador.vitorias.desc()).all()

    # Se for modo de seleção, mostrar modal para escolher
    if modo == 'selecionar':
        return render_template('selecionar_modo.html')

    return render_template('index.html', jogadores=jogadores, modo=modo)

@app.route('/modo/<modo_escolhido>')
def definir_modo(modo_escolhido):
    """Define o modo do torneio manualmente"""
    if modo_escolhido in ['amistoso', 'oficial']:
        jogadores = Jogador.query.order_by(Jogador.pontuacao.desc(), Jogador.vitorias.desc()).all()
        return render_template('index.html', jogadores=jogadores, modo=modo_escolhido)
    return redirect('/')

@app.route('/amistoso')
def amistoso():
    """Página específica do modo amistoso"""
    jogadores = Jogador.query.order_by(Jogador.pontuacao.desc(), Jogador.vitorias.desc()).all()
    return render_template('amistoso.html', jogadores=jogadores)

@app.route('/jogadores')
def jogadores():
    """Página de gerenciamento de jogadores"""
    jogadores = Jogador.query.all()
    return render_template('jogadores.html', jogadores=jogadores, tipos=TIPOS_POKEMON)

@app.route('/batalhas')
def batalhas():
    """Página de registro de batalhas"""
    jogadores = Jogador.query.all()
    batalhas = Batalha.query.order_by(Batalha.created_at.desc()).all()
    return render_template('batalhas.html', jogadores=jogadores, batalhas=batalhas)

@app.route('/repescagem')
def repescagem():
    """Página da chave de repescagem e finais"""
    # Buscar classificação final da fase classificatória
    jogadores = Jogador.query.order_by(Jogador.pontuacao.desc(), Jogador.vitorias.desc()).all()
    repescagens = Repescagem.query.all()
    return render_template('repescagem.html', jogadores=jogadores, repescagens=repescagens)

# API Routes
@app.route('/api/upload-imagem', methods=['POST'])
def upload_imagem():
    """Upload de imagem do jogador"""
    if 'imagem' not in request.files:
        return jsonify({'error': 'Nenhuma imagem enviada'}), 400

    arquivo = request.files['imagem']
    if arquivo.filename == '':
        return jsonify({'error': 'Nenhuma imagem selecionada'}), 400

    # Verificar se é uma imagem válida
    extensoes_permitidas = {'png', 'jpg', 'jpeg', 'gif', 'webp'}
    if '.' not in arquivo.filename or arquivo.filename.rsplit('.', 1)[1].lower() not in extensoes_permitidas:
        return jsonify({'error': 'Formato de imagem não suportado'}), 400

    try:
        caminho_imagem = salvar_imagem_jogador(arquivo)
        return jsonify({'caminho': caminho_imagem}), 200
    except Exception as e:
        return jsonify({'error': f'Erro ao salvar imagem: {str(e)}'}), 500

@app.route('/api/jogadores', methods=['GET', 'POST'])
def api_jogadores():
    if request.method == 'GET':
        jogadores = Jogador.query.all()
        return jsonify([j.to_dict() for j in jogadores])

    elif request.method == 'POST':
        data = request.get_json()

        # Validar dados obrigatórios
        if not all(k in data for k in ['nome_real', 'nickname', 'tipo_primario']):
            return jsonify({'error': 'Dados obrigatórios faltando'}), 400

        # Verificar se nickname já existe
        if Jogador.query.filter_by(nickname=data['nickname']).first():
            return jsonify({'error': 'Nickname já existe'}), 400

        jogador = Jogador(
            nome_real=data['nome_real'],
            nickname=data['nickname'],
            tipo_primario=data['tipo_primario'],
            tipo_secundario=data.get('tipo_secundario'),
            foto_perfil=data.get('foto_perfil')
        )

        db.session.add(jogador)
        db.session.commit()

        return jsonify(jogador.to_dict()), 201

@app.route('/api/jogadores/<int:jogador_id>', methods=['GET', 'PUT', 'DELETE'])
def api_jogador(jogador_id):
    jogador = Jogador.query.get_or_404(jogador_id)
    
    if request.method == 'GET':
        return jsonify(jogador.to_dict())
    
    elif request.method == 'PUT':
        data = request.get_json()
        
        jogador.nome_real = data.get('nome_real', jogador.nome_real)
        jogador.nickname = data.get('nickname', jogador.nickname)
        jogador.tipo_primario = data.get('tipo_primario', jogador.tipo_primario)
        jogador.tipo_secundario = data.get('tipo_secundario', jogador.tipo_secundario)
        jogador.foto_perfil = data.get('foto_perfil', jogador.foto_perfil)
        
        db.session.commit()
        return jsonify(jogador.to_dict())
    
    elif request.method == 'DELETE':
        db.session.delete(jogador)
        db.session.commit()
        return '', 204

@app.route('/api/pokemons', methods=['GET', 'POST'])
def api_pokemons():
    if request.method == 'GET':
        jogador_id = request.args.get('jogador_id')
        if jogador_id:
            pokemons = Pokemon.query.filter_by(jogador_id=jogador_id).all()
        else:
            pokemons = Pokemon.query.all()
        return jsonify([p.to_dict() for p in pokemons])
    
    elif request.method == 'POST':
        data = request.get_json()
        
        if not all(k in data for k in ['nome', 'tipo_primario', 'jogador_id']):
            return jsonify({'error': 'Dados obrigatórios faltando'}), 400
        
        pokemon = Pokemon(
            nome=data['nome'],
            tipo_primario=data['tipo_primario'],
            tipo_secundario=data.get('tipo_secundario'),
            apelido=data.get('apelido'),
            imagem=data.get('imagem'),
            credenciado_finais=data.get('credenciado_finais', False),
            jogador_id=data['jogador_id']
        )
        
        db.session.add(pokemon)
        db.session.commit()
        
        return jsonify(pokemon.to_dict()), 201

@app.route('/api/pokemons/<int:pokemon_id>', methods=['DELETE'])
def api_pokemon_delete(pokemon_id):
    pokemon = Pokemon.query.get_or_404(pokemon_id)
    db.session.delete(pokemon)
    db.session.commit()
    return '', 204

@app.route('/api/batalhas', methods=['GET', 'POST'])
def api_batalhas():
    if request.method == 'GET':
        batalhas = Batalha.query.order_by(Batalha.created_at.desc()).all()
        return jsonify([b.to_dict() for b in batalhas])
    
    elif request.method == 'POST':
        data = request.get_json()
        
        # Validar dados obrigatórios
        required_fields = ['player_a_id', 'player_b_id', 'pokemon_a1', 'pokemon_a2', 
                          'pokemon_b1', 'pokemon_b2', 'vencedor']
        if not all(k in data for k in required_fields):
            return jsonify({'error': 'Dados obrigatórios faltando'}), 400
        
        batalha = Batalha(
            player_a_id=data['player_a_id'],
            player_b_id=data['player_b_id'],
            pokemon_a1=data['pokemon_a1'],
            pokemon_a2=data['pokemon_a2'],
            pokemon_b1=data['pokemon_b1'],
            pokemon_b2=data['pokemon_b2'],
            vencedor=data['vencedor'],
            pokemon_destaque=data.get('pokemon_destaque'),
            fase=data.get('fase', 'classificatoria')
        )
        
        db.session.add(batalha)
        db.session.commit()
        
        # Atualizar estatísticas dos jogadores
        jogador_a = Jogador.query.get(data['player_a_id'])
        jogador_b = Jogador.query.get(data['player_b_id'])
        
        jogador_a.atualizar_estatisticas()
        jogador_b.atualizar_estatisticas()
        
        db.session.commit()
        
        return jsonify(batalha.to_dict()), 201

@app.route('/api/classificacao')
def api_classificacao():
    """Retorna a classificação atual ordenada por pontos"""
    jogadores = Jogador.query.order_by(
        Jogador.pontuacao.desc(), 
        Jogador.vitorias.desc(),
        Jogador.derrotas.asc()
    ).all()
    
    classificacao = []
    for i, jogador in enumerate(jogadores, 1):
        jogador_dict = jogador.to_dict()
        jogador_dict['posicao'] = i
        classificacao.append(jogador_dict)
    
    return jsonify(classificacao)

@app.route('/api/tipos')
def api_tipos():
    """Retorna lista de tipos Pokémon válidos"""
    return jsonify(TIPOS_POKEMON)

@app.route('/api/modo-torneio')
def api_modo_torneio():
    """Retorna o modo atual do torneio"""
    modo = detectar_modo_torneio()
    hoje = date.today()

    return jsonify({
        'modo': modo,
        'data_atual': hoje.strftime('%d/%m/%Y'),
        'data_amistoso': DATA_AMISTOSO.strftime('%d/%m/%Y'),
        'data_oficial': DATA_OFICIAL.strftime('%d/%m/%Y'),
        'is_amistoso': hoje == DATA_AMISTOSO,
        'is_oficial': hoje == DATA_OFICIAL
    })

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=5000)
