from flask import Flask, render_template, request, jsonify, redirect, url_for, flash
from flask_cors import CORS
from werkzeug.utils import secure_filename
import os
from models import db, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, Batal<PERSON>, Repescagem
from datetime import datetime

app = Flask(__name__)
app.config['SECRET_KEY'] = 'pokemon_vgc_championship_2024'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///campeonato_pokemon.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False
app.config['UPLOAD_FOLDER'] = 'static/uploads'
app.config['MAX_CONTENT_LENGTH'] = 16 * 1024 * 1024  # 16MB max file size

# Configurar CORS
CORS(app)

# Inicializar banco de dados
db.init_app(app)

# Criar pastas necessárias
os.makedirs('static/uploads/jogadores', exist_ok=True)
os.makedirs('static/uploads/pokemons', exist_ok=True)

# Tipos Pokémon válidos
TIPOS_POKEMON = [
    'Normal', 'Fire', 'Water', 'Electric', 'Grass', 'Ice',
    'Fighting', 'Poison', 'Ground', 'Flying', 'Psychic', 'Bug',
    'Rock', 'Ghost', 'Dragon', 'Dark', 'Steel', 'Fairy'
]

@app.route('/')
def index():
    """Página principal com tabela classificatória"""
    jogadores = Jogador.query.order_by(Jogador.pontuacao.desc(), Jogador.vitorias.desc()).all()
    return render_template('index.html', jogadores=jogadores)

@app.route('/jogadores')
def jogadores():
    """Página de gerenciamento de jogadores"""
    jogadores = Jogador.query.all()
    return render_template('jogadores.html', jogadores=jogadores, tipos=TIPOS_POKEMON)

@app.route('/batalhas')
def batalhas():
    """Página de registro de batalhas"""
    jogadores = Jogador.query.all()
    batalhas = Batalha.query.order_by(Batalha.created_at.desc()).all()
    return render_template('batalhas.html', jogadores=jogadores, batalhas=batalhas)

@app.route('/repescagem')
def repescagem():
    """Página da chave de repescagem e finais"""
    # Buscar classificação final da fase classificatória
    jogadores = Jogador.query.order_by(Jogador.pontuacao.desc(), Jogador.vitorias.desc()).all()
    repescagens = Repescagem.query.all()
    return render_template('repescagem.html', jogadores=jogadores, repescagens=repescagens)

# API Routes
@app.route('/api/jogadores', methods=['GET', 'POST'])
def api_jogadores():
    if request.method == 'GET':
        jogadores = Jogador.query.all()
        return jsonify([j.to_dict() for j in jogadores])
    
    elif request.method == 'POST':
        data = request.get_json()
        
        # Validar dados obrigatórios
        if not all(k in data for k in ['nome_real', 'nickname', 'tipo_primario']):
            return jsonify({'error': 'Dados obrigatórios faltando'}), 400
        
        # Verificar se nickname já existe
        if Jogador.query.filter_by(nickname=data['nickname']).first():
            return jsonify({'error': 'Nickname já existe'}), 400
        
        jogador = Jogador(
            nome_real=data['nome_real'],
            nickname=data['nickname'],
            tipo_primario=data['tipo_primario'],
            tipo_secundario=data.get('tipo_secundario'),
            foto_perfil=data.get('foto_perfil')
        )
        
        db.session.add(jogador)
        db.session.commit()
        
        return jsonify(jogador.to_dict()), 201

@app.route('/api/jogadores/<int:jogador_id>', methods=['GET', 'PUT', 'DELETE'])
def api_jogador(jogador_id):
    jogador = Jogador.query.get_or_404(jogador_id)
    
    if request.method == 'GET':
        return jsonify(jogador.to_dict())
    
    elif request.method == 'PUT':
        data = request.get_json()
        
        jogador.nome_real = data.get('nome_real', jogador.nome_real)
        jogador.nickname = data.get('nickname', jogador.nickname)
        jogador.tipo_primario = data.get('tipo_primario', jogador.tipo_primario)
        jogador.tipo_secundario = data.get('tipo_secundario', jogador.tipo_secundario)
        jogador.foto_perfil = data.get('foto_perfil', jogador.foto_perfil)
        
        db.session.commit()
        return jsonify(jogador.to_dict())
    
    elif request.method == 'DELETE':
        db.session.delete(jogador)
        db.session.commit()
        return '', 204

@app.route('/api/pokemons', methods=['GET', 'POST'])
def api_pokemons():
    if request.method == 'GET':
        jogador_id = request.args.get('jogador_id')
        if jogador_id:
            pokemons = Pokemon.query.filter_by(jogador_id=jogador_id).all()
        else:
            pokemons = Pokemon.query.all()
        return jsonify([p.to_dict() for p in pokemons])
    
    elif request.method == 'POST':
        data = request.get_json()
        
        if not all(k in data for k in ['nome', 'tipo_primario', 'jogador_id']):
            return jsonify({'error': 'Dados obrigatórios faltando'}), 400
        
        pokemon = Pokemon(
            nome=data['nome'],
            tipo_primario=data['tipo_primario'],
            tipo_secundario=data.get('tipo_secundario'),
            apelido=data.get('apelido'),
            imagem=data.get('imagem'),
            credenciado_finais=data.get('credenciado_finais', False),
            jogador_id=data['jogador_id']
        )
        
        db.session.add(pokemon)
        db.session.commit()
        
        return jsonify(pokemon.to_dict()), 201

@app.route('/api/pokemons/<int:pokemon_id>', methods=['DELETE'])
def api_pokemon_delete(pokemon_id):
    pokemon = Pokemon.query.get_or_404(pokemon_id)
    db.session.delete(pokemon)
    db.session.commit()
    return '', 204

@app.route('/api/batalhas', methods=['GET', 'POST'])
def api_batalhas():
    if request.method == 'GET':
        batalhas = Batalha.query.order_by(Batalha.created_at.desc()).all()
        return jsonify([b.to_dict() for b in batalhas])
    
    elif request.method == 'POST':
        data = request.get_json()
        
        # Validar dados obrigatórios
        required_fields = ['player_a_id', 'player_b_id', 'pokemon_a1', 'pokemon_a2', 
                          'pokemon_b1', 'pokemon_b2', 'vencedor']
        if not all(k in data for k in required_fields):
            return jsonify({'error': 'Dados obrigatórios faltando'}), 400
        
        batalha = Batalha(
            player_a_id=data['player_a_id'],
            player_b_id=data['player_b_id'],
            pokemon_a1=data['pokemon_a1'],
            pokemon_a2=data['pokemon_a2'],
            pokemon_b1=data['pokemon_b1'],
            pokemon_b2=data['pokemon_b2'],
            vencedor=data['vencedor'],
            pokemon_destaque=data.get('pokemon_destaque'),
            fase=data.get('fase', 'classificatoria')
        )
        
        db.session.add(batalha)
        db.session.commit()
        
        # Atualizar estatísticas dos jogadores
        jogador_a = Jogador.query.get(data['player_a_id'])
        jogador_b = Jogador.query.get(data['player_b_id'])
        
        jogador_a.atualizar_estatisticas()
        jogador_b.atualizar_estatisticas()
        
        db.session.commit()
        
        return jsonify(batalha.to_dict()), 201

@app.route('/api/classificacao')
def api_classificacao():
    """Retorna a classificação atual ordenada por pontos"""
    jogadores = Jogador.query.order_by(
        Jogador.pontuacao.desc(), 
        Jogador.vitorias.desc(),
        Jogador.derrotas.asc()
    ).all()
    
    classificacao = []
    for i, jogador in enumerate(jogadores, 1):
        jogador_dict = jogador.to_dict()
        jogador_dict['posicao'] = i
        classificacao.append(jogador_dict)
    
    return jsonify(classificacao)

@app.route('/api/tipos')
def api_tipos():
    """Retorna lista de tipos Pokémon válidos"""
    return jsonify(TIPOS_POKEMON)

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
    app.run(debug=True, host='0.0.0.0', port=5000)
