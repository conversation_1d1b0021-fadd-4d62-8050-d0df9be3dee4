{% extends "base.html" %}

{% block title %}Estatísticas - Torneio BAGREMON{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <h2 class="text-4xl font-bold text-white mb-2">
            <i class="fas fa-chart-bar text-blue-400 mr-3"></i>
            Estatísticas Avançadas
        </h2>
        <p class="text-white/80 text-lg">Análise completa do desempenho no Torneio BAGREMON</p>
    </div>

    <!-- Estatísticas Gerais -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="total-jogadores-stat">{{ jogadores|length }}</div>
            <div class="text-white/70 text-sm">Total de Jogadores</div>
        </div>
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="total-batalhas-stat">{{ batalhas|length }}</div>
            <div class="text-white/70 text-sm">Total de Batalhas</div>
        </div>
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="total-pokemons-stat">0</div>
            <div class="text-white/70 text-sm">Total de Pokémon</div>
        </div>
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="media-vitorias">0</div>
            <div class="text-white/70 text-sm">Média de Vitórias</div>
        </div>
    </div>

    <!-- Gráficos e Análises -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Top Jogadores -->
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-6">
            <h3 class="text-xl font-semibold text-white mb-4">
                <i class="fas fa-crown mr-2"></i>
                Top 5 Jogadores
            </h3>
            <div id="top-jogadores" class="space-y-3">
                <!-- Será preenchido via JavaScript -->
            </div>
        </div>

        <!-- Pokémon Mais Usados -->
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-6">
            <h3 class="text-xl font-semibold text-white mb-4">
                <i class="fas fa-star mr-2"></i>
                Pokémon Mais Destacados
            </h3>
            <div id="pokemon-populares" class="space-y-3">
                <!-- Será preenchido via JavaScript -->
            </div>
        </div>

        <!-- Tipos Mais Populares -->
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-6">
            <h3 class="text-xl font-semibold text-white mb-4">
                <i class="fas fa-fire mr-2"></i>
                Tipos Mais Populares
            </h3>
            <div id="tipos-populares" class="space-y-3">
                <!-- Será preenchido via JavaScript -->
            </div>
        </div>

        <!-- Estatísticas de Batalhas -->
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-6">
            <h3 class="text-xl font-semibold text-white mb-4">
                <i class="fas fa-swords mr-2"></i>
                Análise de Batalhas
            </h3>
            <div id="stats-batalhas" class="space-y-3">
                <!-- Será preenchido via JavaScript -->
            </div>
        </div>
    </div>

    <!-- Tabela Detalhada de Jogadores -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-white/20 flex justify-between items-center">
            <h3 class="text-xl font-semibold text-white">
                <i class="fas fa-table mr-2"></i>
                Estatísticas Detalhadas por Jogador
            </h3>
            <button onclick="exportarEstatisticas()" 
                    class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded-lg text-sm">
                <i class="fas fa-download mr-2"></i>
                Exportar
            </button>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-black/20">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-white/80 uppercase tracking-wider">Jogador</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-white/80 uppercase tracking-wider">V/D</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-white/80 uppercase tracking-wider">% Vitórias</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-white/80 uppercase tracking-wider">Pontos</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-white/80 uppercase tracking-wider">Pokémon</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-white/80 uppercase tracking-wider">Destaque</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-white/80 uppercase tracking-wider">Ações</th>
                    </tr>
                </thead>
                <tbody id="tabela-estatisticas" class="divide-y divide-white/10">
                    <!-- Será preenchido via JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Histórico de Batalhas Recentes -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-white/20">
            <h3 class="text-xl font-semibold text-white">
                <i class="fas fa-history mr-2"></i>
                Últimas 10 Batalhas
            </h3>
        </div>
        
        <div class="p-6">
            <div id="historico-recente" class="space-y-4">
                <!-- Será preenchido via JavaScript -->
            </div>
        </div>
    </div>

    <!-- Botões de Ação -->
    <div class="text-center space-x-4">
        <button onclick="atualizarEstatisticas()" 
                class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
            <i class="fas fa-sync-alt mr-2"></i>
            Atualizar Dados
        </button>
        
        <button onclick="gerarRelatorioCompleto()" 
                class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
            <i class="fas fa-file-pdf mr-2"></i>
            Relatório PDF
        </button>
        
        <a href="/" 
           class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition-colors inline-block">
            <i class="fas fa-arrow-left mr-2"></i>
            Voltar ao Início
        </a>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Função para carregar estatísticas
async function carregarEstatisticas() {
    try {
        const [jogadoresRes, batalhasRes] = await Promise.all([
            fetch('/api/jogadores'),
            fetch('/api/batalhas')
        ]);
        
        const jogadores = await jogadoresRes.json();
        const batalhas = await batalhasRes.json();
        
        // Calcular estatísticas gerais
        const totalPokemons = jogadores.reduce((total, j) => total + j.pokemons.length, 0);
        const mediaVitorias = jogadores.length > 0 ? 
            (jogadores.reduce((total, j) => total + j.vitorias, 0) / jogadores.length).toFixed(1) : 0;
        
        document.getElementById('total-pokemons-stat').textContent = totalPokemons;
        document.getElementById('media-vitorias').textContent = mediaVitorias;
        
        // Criar top jogadores
        criarTopJogadores(jogadores);
        
        // Criar pokémon populares
        criarPokemonPopulares(batalhas);
        
        // Criar tipos populares
        criarTiposPopulares(jogadores);
        
        // Criar stats de batalhas
        criarStatsBatalhas(batalhas);
        
        // Criar tabela detalhada
        criarTabelaDetalhada(jogadores);
        
        // Criar histórico recente
        criarHistoricoRecente(batalhas.slice(0, 10));
        
    } catch (error) {
        console.error('Erro ao carregar estatísticas:', error);
        mostrarNotificacao('Erro ao carregar estatísticas.', 'error');
    }
}

// Função para criar top jogadores
function criarTopJogadores(jogadores) {
    const top5 = jogadores
        .sort((a, b) => b.pontuacao - a.pontuacao || b.vitorias - a.vitorias)
        .slice(0, 5);
    
    const container = document.getElementById('top-jogadores');
    container.innerHTML = '';
    
    top5.forEach((jogador, index) => {
        const div = document.createElement('div');
        div.className = 'flex items-center justify-between p-3 bg-white/5 rounded-lg';
        
        const medalhas = ['🥇', '🥈', '🥉', '4️⃣', '5️⃣'];
        const percentual = jogador.vitorias + jogador.derrotas > 0 ? 
            ((jogador.vitorias / (jogador.vitorias + jogador.derrotas)) * 100).toFixed(1) : 0;
        
        div.innerHTML = `
            <div class="flex items-center">
                <span class="text-2xl mr-3">${medalhas[index]}</span>
                <div>
                    <div class="text-white font-medium">${jogador.nickname}</div>
                    <div class="text-white/60 text-sm">${jogador.vitorias}V - ${jogador.derrotas}D</div>
                </div>
            </div>
            <div class="text-right">
                <div class="text-yellow-400 font-bold">${jogador.pontuacao} pts</div>
                <div class="text-white/60 text-sm">${percentual}%</div>
            </div>
        `;
        
        container.appendChild(div);
    });
}

// Função para criar pokémon populares
function criarPokemonPopulares(batalhas) {
    const pokemonCount = {};
    
    batalhas.forEach(batalha => {
        if (batalha.pokemon_destaque) {
            pokemonCount[batalha.pokemon_destaque] = (pokemonCount[batalha.pokemon_destaque] || 0) + 1;
        }
    });
    
    const topPokemon = Object.entries(pokemonCount)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5);
    
    const container = document.getElementById('pokemon-populares');
    container.innerHTML = '';
    
    if (topPokemon.length === 0) {
        container.innerHTML = '<p class="text-white/60 text-center">Nenhum Pokémon destacado ainda</p>';
        return;
    }
    
    topPokemon.forEach(([pokemon, count], index) => {
        const div = document.createElement('div');
        div.className = 'flex items-center justify-between p-3 bg-white/5 rounded-lg';
        
        div.innerHTML = `
            <div class="flex items-center">
                <span class="text-lg mr-3">${index + 1}º</span>
                <div>
                    <div class="text-white font-medium">${pokemon}</div>
                    <div class="text-white/60 text-sm">Pokémon Destaque</div>
                </div>
            </div>
            <div class="text-yellow-400 font-bold">${count}x</div>
        `;
        
        container.appendChild(div);
    });
}

// Função para criar tipos populares
function criarTiposPopulares(jogadores) {
    const tipoCount = {};
    
    jogadores.forEach(jogador => {
        tipoCount[jogador.tipo_primario] = (tipoCount[jogador.tipo_primario] || 0) + 1;
        if (jogador.tipo_secundario) {
            tipoCount[jogador.tipo_secundario] = (tipoCount[jogador.tipo_secundario] || 0) + 1;
        }
    });
    
    const topTipos = Object.entries(tipoCount)
        .sort(([,a], [,b]) => b - a)
        .slice(0, 5);
    
    const container = document.getElementById('tipos-populares');
    container.innerHTML = '';
    
    topTipos.forEach(([tipo, count], index) => {
        const div = document.createElement('div');
        div.className = 'flex items-center justify-between p-3 bg-white/5 rounded-lg';
        
        div.innerHTML = `
            <div class="flex items-center">
                <span class="text-lg mr-3">${index + 1}º</span>
                <div>
                    <span class="tipo-badge ${getTipoClass(tipo)}">${tipo}</span>
                </div>
            </div>
            <div class="text-white font-bold">${count} jogadores</div>
        `;
        
        container.appendChild(div);
    });
}

// Função para criar stats de batalhas
function criarStatsBatalhas(batalhas) {
    const container = document.getElementById('stats-batalhas');
    container.innerHTML = '';
    
    const hoje = new Date().toDateString();
    const batalhasHoje = batalhas.filter(b => 
        new Date(b.created_at).toDateString() === hoje
    ).length;
    
    const faseCount = {};
    batalhas.forEach(b => {
        faseCount[b.fase] = (faseCount[b.fase] || 0) + 1;
    });
    
    const stats = [
        { label: 'Batalhas Hoje', valor: batalhasHoje, cor: 'text-green-400' },
        { label: 'Classificatórias', valor: faseCount.classificatoria || 0, cor: 'text-blue-400' },
        { label: 'Repescagem', valor: faseCount.repescagem || 0, cor: 'text-yellow-400' },
        { label: 'Finais', valor: (faseCount.semifinal || 0) + (faseCount.final || 0), cor: 'text-red-400' }
    ];
    
    stats.forEach(stat => {
        const div = document.createElement('div');
        div.className = 'flex justify-between items-center p-2';
        div.innerHTML = `
            <span class="text-white/80">${stat.label}:</span>
            <span class="${stat.cor} font-bold">${stat.valor}</span>
        `;
        container.appendChild(div);
    });
}

// Função para criar tabela detalhada
function criarTabelaDetalhada(jogadores) {
    const tbody = document.getElementById('tabela-estatisticas');
    tbody.innerHTML = '';
    
    jogadores.forEach(jogador => {
        const totalPartidas = jogador.vitorias + jogador.derrotas;
        const percentual = totalPartidas > 0 ? 
            ((jogador.vitorias / totalPartidas) * 100).toFixed(1) : 0;
        
        const row = document.createElement('tr');
        row.className = 'hover:bg-white/5 transition-colors';
        row.innerHTML = `
            <td class="px-6 py-4">
                <div class="flex items-center">
                    <img class="h-8 w-8 rounded-full object-cover mr-3" 
                         src="${jogador.foto_perfil || '/static/uploads/default-avatar.png'}" 
                         alt="${jogador.nickname}">
                    <div>
                        <div class="text-white font-medium">${jogador.nickname}</div>
                        <div class="text-white/60 text-sm">${jogador.nome_real}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 text-center">
                <span class="text-green-400">${jogador.vitorias}</span> / 
                <span class="text-red-400">${jogador.derrotas}</span>
            </td>
            <td class="px-6 py-4 text-center text-white">${percentual}%</td>
            <td class="px-6 py-4 text-center text-yellow-400 font-bold">${jogador.pontuacao}</td>
            <td class="px-6 py-4 text-center text-white">${jogador.pokemons.length}</td>
            <td class="px-6 py-4 text-white">${jogador.pokemon_destaque || '-'}</td>
            <td class="px-6 py-4 text-center">
                <button onclick="verDetalhesJogador(${jogador.id})" 
                        class="text-blue-400 hover:text-blue-300">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        `;
        tbody.appendChild(row);
    });
}

// Função para criar histórico recente
function criarHistoricoRecente(batalhas) {
    const container = document.getElementById('historico-recente');
    container.innerHTML = '';
    
    if (batalhas.length === 0) {
        container.innerHTML = '<p class="text-white/60 text-center">Nenhuma batalha registrada</p>';
        return;
    }
    
    batalhas.forEach(batalha => {
        const div = document.createElement('div');
        div.className = 'bg-white/5 rounded-lg p-4';
        
        const data = new Date(batalha.created_at).toLocaleString('pt-BR');
        const vencedor = batalha.vencedor === 'A' ? batalha.jogador_a.nickname : batalha.jogador_b.nickname;
        
        div.innerHTML = `
            <div class="flex justify-between items-center mb-2">
                <span class="text-white/60 text-sm">${data}</span>
                <span class="bg-${batalha.fase === 'final' ? 'red' : 'blue'}-500 text-white px-2 py-1 rounded text-xs">
                    ${batalha.fase.charAt(0).toUpperCase() + batalha.fase.slice(1)}
                </span>
            </div>
            <div class="flex items-center justify-between">
                <span class="text-white">${batalha.jogador_a.nickname}</span>
                <span class="text-white/60 mx-4">VS</span>
                <span class="text-white">${batalha.jogador_b.nickname}</span>
            </div>
            <div class="text-center mt-2">
                <span class="text-green-400 font-bold">Vencedor: ${vencedor}</span>
                ${batalha.pokemon_destaque ? `<br><span class="text-yellow-400 text-sm">Destaque: ${batalha.pokemon_destaque}</span>` : ''}
            </div>
        `;
        
        container.appendChild(div);
    });
}

// Funções de ação
function verDetalhesJogador(id) {
    // Implementar modal com detalhes do jogador
    alert(`Detalhes do jogador ${id} - Funcionalidade em desenvolvimento`);
}

function exportarEstatisticas() {
    window.open('/api/exportar-jogadores', '_blank');
}

function gerarRelatorioCompleto() {
    alert('Geração de relatório PDF - Funcionalidade em desenvolvimento');
}

function atualizarEstatisticas() {
    carregarEstatisticas();
    mostrarNotificacao('Estatísticas atualizadas!', 'success');
}

// Carregar estatísticas ao carregar a página
document.addEventListener('DOMContentLoaded', carregarEstatisticas);
</script>
{% endblock %}
