{% extends "base.html" %}

{% block title %}Cards dos Jogadores - Torneio BAGREMON{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="text-center">
        <h2 class="text-4xl font-bold text-white mb-2">
            <i class="fas fa-id-card text-purple-400 mr-3"></i>
            Cards dos Jogadores
        </h2>
        <p class="text-white/80 text-lg">Gere cards visuais personalizados para redes sociais</p>
    </div>

    <!-- Ações Gerais -->
    <div class="text-center space-x-4 mb-8">
        <button onclick="gerarTodosCards()" 
                class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
            <i class="fas fa-magic mr-2"></i>
            Gerar Todos os Cards
        </button>
        
        <button onclick="baixarTodosCards()" 
                class="bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
            <i class="fas fa-download mr-2"></i>
            Baixar Todos
        </button>
    </div>

    <!-- Grid de Jogadores -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {% for jogador in jogadores %}
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-6 card-hover" id="card-container-{{ jogador.id }}">
            <!-- Preview do Jogador -->
            <div class="text-center mb-4">
                <img class="h-20 w-20 rounded-full mx-auto object-cover border-2 border-white/20 mb-3" 
                     src="{{ jogador.foto_perfil or '/static/uploads/default-avatar.png' }}" 
                     alt="{{ jogador.nickname }}">
                <h3 class="text-white font-bold text-lg">{{ jogador.nickname }}</h3>
                <p class="text-white/60">{{ jogador.nome_real }}</p>
            </div>
            
            <!-- Tipos -->
            <div class="flex justify-center space-x-2 mb-4">
                <span class="tipo-badge {{ 'tipo-' + jogador.tipo_primario.lower() }}">{{ jogador.tipo_primario }}</span>
                {% if jogador.tipo_secundario %}
                <span class="tipo-badge {{ 'tipo-' + jogador.tipo_secundario.lower() }}">{{ jogador.tipo_secundario }}</span>
                {% endif %}
            </div>
            
            <!-- Estatísticas Resumidas -->
            <div class="grid grid-cols-3 gap-2 text-center text-sm mb-4">
                <div>
                    <div class="text-green-400 font-bold">{{ jogador.vitorias }}</div>
                    <div class="text-white/60">Vitórias</div>
                </div>
                <div>
                    <div class="text-red-400 font-bold">{{ jogador.derrotas }}</div>
                    <div class="text-white/60">Derrotas</div>
                </div>
                <div>
                    <div class="text-yellow-400 font-bold">{{ jogador.pontuacao }}</div>
                    <div class="text-white/60">Pontos</div>
                </div>
            </div>
            
            <!-- Card Gerado (se existir) -->
            <div id="card-preview-{{ jogador.id }}" class="hidden mb-4">
                <img id="card-image-{{ jogador.id }}" class="w-full rounded-lg border-2 border-white/20" alt="Card do {{ jogador.nickname }}">
            </div>
            
            <!-- Botões de Ação -->
            <div class="space-y-2">
                <button onclick="gerarCard({{ jogador.id }})" 
                        id="btn-gerar-{{ jogador.id }}"
                        class="w-full bg-blue-500 hover:bg-blue-600 text-white py-2 px-4 rounded-lg text-sm transition-colors">
                    <i class="fas fa-magic mr-2"></i>
                    Gerar Card
                </button>
                
                <button onclick="baixarCard({{ jogador.id }})" 
                        id="btn-baixar-{{ jogador.id }}"
                        class="w-full bg-green-500 hover:bg-green-600 text-white py-2 px-4 rounded-lg text-sm transition-colors hidden">
                    <i class="fas fa-download mr-2"></i>
                    Baixar Card
                </button>
                
                <button onclick="compartilharCard({{ jogador.id }})" 
                        id="btn-compartilhar-{{ jogador.id }}"
                        class="w-full bg-purple-500 hover:bg-purple-600 text-white py-2 px-4 rounded-lg text-sm transition-colors hidden">
                    <i class="fas fa-share mr-2"></i>
                    Compartilhar
                </button>
            </div>
        </div>
        {% endfor %}
    </div>

    <!-- Seção de Cards Gerados -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg p-6" id="cards-gerados" style="display: none;">
        <h3 class="text-xl font-semibold text-white mb-4">
            <i class="fas fa-images mr-2"></i>
            Cards Gerados Recentemente
        </h3>
        <div id="galeria-cards" class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            <!-- Cards gerados aparecerão aqui -->
        </div>
    </div>

    <!-- Modal de Visualização -->
    <div id="modal-card" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
        <div class="flex items-center justify-center min-h-screen p-4">
            <div class="bg-white rounded-lg max-w-md w-full p-6">
                <div class="flex justify-between items-center mb-4">
                    <h3 class="text-xl font-bold text-gray-800">Visualizar Card</h3>
                    <button onclick="fecharModalCard()" class="text-gray-500 hover:text-gray-700">
                        <i class="fas fa-times"></i>
                    </button>
                </div>
                
                <div class="text-center">
                    <img id="modal-card-image" class="w-full rounded-lg mb-4" alt="Card">
                    <div class="space-x-2">
                        <button onclick="baixarCardModal()" 
                                class="bg-green-500 hover:bg-green-600 text-white px-4 py-2 rounded">
                            <i class="fas fa-download mr-2"></i>
                            Baixar
                        </button>
                        <button onclick="compartilharCardModal()" 
                                class="bg-purple-500 hover:bg-purple-600 text-white px-4 py-2 rounded">
                            <i class="fas fa-share mr-2"></i>
                            Compartilhar
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Botão Voltar -->
    <div class="text-center">
        <a href="/" 
           class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition-colors inline-block">
            <i class="fas fa-arrow-left mr-2"></i>
            Voltar ao Início
        </a>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let cardsGerados = {};
let cardAtualModal = null;

// Função para gerar card individual
async function gerarCard(jogadorId) {
    const btnGerar = document.getElementById(`btn-gerar-${jogadorId}`);
    const btnBaixar = document.getElementById(`btn-baixar-${jogadorId}`);
    const btnCompartilhar = document.getElementById(`btn-compartilhar-${jogadorId}`);
    
    try {
        // Mostrar loading
        btnGerar.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>Gerando...';
        btnGerar.disabled = true;
        
        const response = await fetch(`/api/gerar-card/${jogadorId}`, {
            method: 'POST'
        });
        
        const result = await response.json();
        
        if (result.sucesso) {
            // Mostrar preview do card
            const preview = document.getElementById(`card-preview-${jogadorId}`);
            const image = document.getElementById(`card-image-${jogadorId}`);
            
            image.src = result.caminho;
            preview.classList.remove('hidden');
            
            // Mostrar botões de ação
            btnBaixar.classList.remove('hidden');
            btnCompartilhar.classList.remove('hidden');
            
            // Armazenar caminho do card
            cardsGerados[jogadorId] = result.caminho;
            
            // Adicionar à galeria
            adicionarCardGaleria(jogadorId, result.caminho);
            
            mostrarNotificacao('Card gerado com sucesso!', 'success');
        } else {
            mostrarNotificacao('Erro ao gerar card: ' + result.erro, 'error');
        }
        
    } catch (error) {
        console.error('Erro ao gerar card:', error);
        mostrarNotificacao('Erro ao gerar card.', 'error');
    } finally {
        // Restaurar botão
        btnGerar.innerHTML = '<i class="fas fa-magic mr-2"></i>Gerar Card';
        btnGerar.disabled = false;
    }
}

// Função para gerar todos os cards
async function gerarTodosCards() {
    const jogadores = document.querySelectorAll('[id^="card-container-"]');
    let gerados = 0;
    
    mostrarNotificacao('Gerando todos os cards...', 'info');
    
    for (const container of jogadores) {
        const jogadorId = container.id.split('-')[2];
        try {
            await gerarCard(jogadorId);
            gerados++;
        } catch (error) {
            console.error(`Erro ao gerar card do jogador ${jogadorId}:`, error);
        }
        
        // Pequena pausa entre gerações
        await new Promise(resolve => setTimeout(resolve, 500));
    }
    
    mostrarNotificacao(`${gerados} cards gerados com sucesso!`, 'success');
}

// Função para baixar card individual
function baixarCard(jogadorId) {
    const caminho = cardsGerados[jogadorId];
    if (caminho) {
        const link = document.createElement('a');
        link.href = caminho;
        link.download = `card_jogador_${jogadorId}.png`;
        link.click();
    }
}

// Função para baixar todos os cards
function baixarTodosCards() {
    const cardsDisponiveis = Object.keys(cardsGerados);
    
    if (cardsDisponiveis.length === 0) {
        mostrarNotificacao('Nenhum card foi gerado ainda.', 'warning');
        return;
    }
    
    cardsDisponiveis.forEach(jogadorId => {
        setTimeout(() => baixarCard(jogadorId), 100 * jogadorId);
    });
    
    mostrarNotificacao(`Baixando ${cardsDisponiveis.length} cards...`, 'success');
}

// Função para compartilhar card
async function compartilharCard(jogadorId) {
    const caminho = cardsGerados[jogadorId];
    
    if (navigator.share) {
        try {
            await navigator.share({
                title: 'Card do Torneio BAGREMON',
                text: 'Confira meu card do Torneio BAGREMON!',
                url: window.location.origin + caminho
            });
        } catch (error) {
            console.log('Compartilhamento cancelado');
        }
    } else {
        // Fallback: copiar link
        const url = window.location.origin + caminho;
        navigator.clipboard.writeText(url).then(() => {
            mostrarNotificacao('Link do card copiado!', 'success');
        });
    }
}

// Função para adicionar card à galeria
function adicionarCardGaleria(jogadorId, caminho) {
    const galeria = document.getElementById('galeria-cards');
    const cardsGeradosSection = document.getElementById('cards-gerados');
    
    // Mostrar seção se estiver oculta
    cardsGeradosSection.style.display = 'block';
    
    // Verificar se já existe
    if (document.getElementById(`galeria-card-${jogadorId}`)) {
        return;
    }
    
    const div = document.createElement('div');
    div.id = `galeria-card-${jogadorId}`;
    div.className = 'relative group cursor-pointer';
    div.onclick = () => visualizarCard(caminho);
    
    div.innerHTML = `
        <img src="${caminho}" class="w-full rounded-lg transition-transform group-hover:scale-105" alt="Card">
        <div class="absolute inset-0 bg-black/50 opacity-0 group-hover:opacity-100 transition-opacity rounded-lg flex items-center justify-center">
            <i class="fas fa-eye text-white text-2xl"></i>
        </div>
    `;
    
    galeria.appendChild(div);
}

// Função para visualizar card no modal
function visualizarCard(caminho) {
    const modal = document.getElementById('modal-card');
    const image = document.getElementById('modal-card-image');
    
    image.src = caminho;
    cardAtualModal = caminho;
    modal.classList.remove('hidden');
}

// Função para fechar modal
function fecharModalCard() {
    document.getElementById('modal-card').classList.add('hidden');
    cardAtualModal = null;
}

// Funções do modal
function baixarCardModal() {
    if (cardAtualModal) {
        const link = document.createElement('a');
        link.href = cardAtualModal;
        link.download = 'card_bagremon.png';
        link.click();
    }
}

async function compartilharCardModal() {
    if (!cardAtualModal) return;
    
    if (navigator.share) {
        try {
            await navigator.share({
                title: 'Card do Torneio BAGREMON',
                text: 'Confira este card do Torneio BAGREMON!',
                url: window.location.origin + cardAtualModal
            });
        } catch (error) {
            console.log('Compartilhamento cancelado');
        }
    } else {
        const url = window.location.origin + cardAtualModal;
        navigator.clipboard.writeText(url).then(() => {
            mostrarNotificacao('Link do card copiado!', 'success');
        });
    }
}

// Fechar modal ao clicar fora
document.addEventListener('click', (e) => {
    if (e.target.id === 'modal-card') {
        fecharModalCard();
    }
});

// Atalho ESC para fechar modal
document.addEventListener('keydown', (e) => {
    if (e.key === 'Escape') {
        fecharModalCard();
    }
});
</script>
{% endblock %}
