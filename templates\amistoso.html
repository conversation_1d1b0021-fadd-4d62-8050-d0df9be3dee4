{% extends "base.html" %}

{% block title %}Modo Amistoso - Torneio BAGREMON{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header do Modo Amistoso -->
    <div class="text-center">
        <h2 class="text-4xl font-bold text-white mb-2">
            <i class="fas fa-handshake text-green-400 mr-3"></i>
            Modo Amistoso
        </h2>
        <p class="text-white/80 text-lg">Todos contra todos - Diversão garantida!</p>
        <div class="mt-2 inline-flex items-center bg-green-500/20 px-4 py-2 rounded-full">
            <i class="fas fa-calendar-day text-green-400 mr-2"></i>
            <span class="text-green-300 font-medium">Data oficial: 05/07/2025</span>
        </div>
    </div>

    <!-- Estatísticas do Amistoso -->
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="total-participantes">{{ jogadores|length }}</div>
            <div class="text-white/70 text-sm">Participantes</div>
        </div>
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="partidas-realizadas">0</div>
            <div class="text-white/70 text-sm">Partidas Realizadas</div>
        </div>
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="partidas-restantes">0</div>
            <div class="text-white/70 text-sm">Partidas Restantes</div>
        </div>
        <div class="bg-white/10 backdrop-blur-md rounded-lg p-4 text-center">
            <div class="text-2xl font-bold text-white" id="lider-amistoso">-</div>
            <div class="text-white/70 text-sm">Líder Atual</div>
        </div>
    </div>

    <!-- Pódio dos 3 Primeiros -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg p-6">
        <h3 class="text-xl font-semibold text-white mb-4 text-center">
            <i class="fas fa-medal mr-2"></i>
            Pódio do Amistoso
        </h3>
        
        <div class="flex justify-center items-end space-x-4" id="podio-amistoso">
            <!-- Será preenchido via JavaScript -->
        </div>
    </div>

    <!-- Ranking Completo -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-white/20">
            <h3 class="text-xl font-semibold text-white">
                <i class="fas fa-list-ol mr-2"></i>
                Ranking Geral do Amistoso
            </h3>
        </div>
        
        <div class="overflow-x-auto">
            <table class="w-full">
                <thead class="bg-black/20">
                    <tr>
                        <th class="px-6 py-3 text-left text-xs font-medium text-white/80 uppercase tracking-wider">Pos</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-white/80 uppercase tracking-wider">Jogador</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-white/80 uppercase tracking-wider">Vitórias</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-white/80 uppercase tracking-wider">Derrotas</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-white/80 uppercase tracking-wider">Pontos</th>
                        <th class="px-6 py-3 text-center text-xs font-medium text-white/80 uppercase tracking-wider">% Vitórias</th>
                        <th class="px-6 py-3 text-left text-xs font-medium text-white/80 uppercase tracking-wider">Prêmio</th>
                    </tr>
                </thead>
                <tbody id="ranking-amistoso" class="divide-y divide-white/10">
                    <!-- Será preenchido via JavaScript -->
                </tbody>
            </table>
        </div>
    </div>

    <!-- Histórico de Partidas -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-white/20">
            <h3 class="text-xl font-semibold text-white">
                <i class="fas fa-history mr-2"></i>
                Histórico de Partidas do Amistoso
            </h3>
        </div>
        
        <div class="p-6">
            <div id="historico-amistoso" class="space-y-4">
                <!-- Será preenchido via JavaScript -->
            </div>
        </div>
    </div>

    <!-- Botões de Ação -->
    <div class="text-center space-x-4">
        <a href="/batalhas" 
           class="bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-lg transition-colors inline-block">
            <i class="fas fa-plus mr-2"></i>
            Registrar Nova Partida
        </a>
        
        <button onclick="exportarRankingAmistoso()" 
                class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
            <i class="fas fa-download mr-2"></i>
            Exportar Ranking
        </button>
        
        <a href="/" 
           class="bg-gray-500 hover:bg-gray-600 text-white font-bold py-3 px-6 rounded-lg transition-colors inline-block">
            <i class="fas fa-arrow-left mr-2"></i>
            Voltar ao Início
        </a>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Prêmios do amistoso
const premiosAmistoso = {
    1: { nome: "🥇 1º Lugar", descricao: "Campeão do Amistoso", cor: "text-yellow-400" },
    2: { nome: "🥈 2º Lugar", descricao: "Vice-Campeão", cor: "text-gray-300" },
    3: { nome: "🥉 3º Lugar", descricao: "Terceiro Colocado", cor: "text-orange-400" }
};

// Função para carregar dados do amistoso
async function carregarDadosAmistoso() {
    try {
        const [jogadoresRes, batalhasRes] = await Promise.all([
            fetch('/api/classificacao'),
            fetch('/api/batalhas')
        ]);
        
        const jogadores = await jogadoresRes.json();
        const batalhas = await batalhasRes.json();
        
        // Filtrar apenas batalhas do amistoso (ou todas se não houver distinção)
        const batalhasAmistoso = batalhas.filter(b => b.fase === 'amistoso' || b.fase === 'classificatoria');
        
        // Atualizar estatísticas
        const totalParticipantes = jogadores.length;
        const partidasRealizadas = batalhasAmistoso.length;
        const totalPartidas = (totalParticipantes * (totalParticipantes - 1)) / 2; // Todos contra todos
        const partidasRestantes = Math.max(0, totalPartidas - partidasRealizadas);
        
        document.getElementById('partidas-realizadas').textContent = partidasRealizadas;
        document.getElementById('partidas-restantes').textContent = partidasRestantes;
        
        if (jogadores.length > 0) {
            document.getElementById('lider-amistoso').textContent = jogadores[0].nickname;
        }
        
        // Criar pódio
        criarPodio(jogadores.slice(0, 3));
        
        // Criar ranking
        criarRankingAmistoso(jogadores);
        
        // Criar histórico
        criarHistoricoAmistoso(batalhasAmistoso);
        
    } catch (error) {
        console.error('Erro ao carregar dados do amistoso:', error);
        mostrarNotificacao('Erro ao carregar dados do amistoso.', 'error');
    }
}

// Função para criar o pódio
function criarPodio(top3) {
    const podio = document.getElementById('podio-amistoso');
    podio.innerHTML = '';
    
    if (top3.length === 0) {
        podio.innerHTML = '<p class="text-white/60 text-center">Nenhuma partida realizada ainda</p>';
        return;
    }
    
    // Ordem do pódio: 2º, 1º, 3º
    const ordemPodio = [
        { jogador: top3[1], posicao: 2, altura: 'h-24', ordem: 1 },
        { jogador: top3[0], posicao: 1, altura: 'h-32', ordem: 2 },
        { jogador: top3[2], posicao: 3, altura: 'h-20', ordem: 3 }
    ].filter(item => item.jogador);
    
    ordemPodio.forEach(item => {
        const premio = premiosAmistoso[item.posicao];
        const div = document.createElement('div');
        div.className = `text-center ${item.ordem === 2 ? 'order-2' : item.ordem === 1 ? 'order-1' : 'order-3'}`;
        div.innerHTML = `
            <div class="bg-white/10 rounded-lg p-4 ${item.altura} flex flex-col justify-end mb-2">
                <img class="h-12 w-12 rounded-full mx-auto object-cover border-2 border-white/20 mb-2" 
                     src="${item.jogador.foto_perfil || '/static/uploads/default-avatar.png'}" 
                     alt="${item.jogador.nickname}">
                <div class="text-white font-bold text-sm">${item.jogador.nickname}</div>
                <div class="text-white/60 text-xs">${item.jogador.pontuacao} pts</div>
            </div>
            <div class="${premio.cor} font-bold text-sm">${premio.nome}</div>
            <div class="text-white/60 text-xs">${premio.descricao}</div>
        `;
        podio.appendChild(div);
    });
}

// Função para criar ranking do amistoso
function criarRankingAmistoso(jogadores) {
    const tbody = document.getElementById('ranking-amistoso');
    tbody.innerHTML = '';
    
    jogadores.forEach((jogador, index) => {
        const posicao = index + 1;
        const totalPartidas = jogador.vitorias + jogador.derrotas;
        const percentualVitorias = totalPartidas > 0 ? ((jogador.vitorias / totalPartidas) * 100).toFixed(1) : 0;
        const premio = premiosAmistoso[posicao];
        
        const row = document.createElement('tr');
        row.className = 'hover:bg-white/5 transition-colors';
        
        // Classe especial para o pódio
        let rowClass = '';
        if (posicao === 1) rowClass = 'bg-yellow-500/10';
        else if (posicao === 2) rowClass = 'bg-gray-400/10';
        else if (posicao === 3) rowClass = 'bg-orange-500/10';
        
        if (rowClass) row.classList.add(rowClass);
        
        row.innerHTML = `
            <td class="px-6 py-4">
                <span class="inline-flex items-center justify-center w-8 h-8 rounded-full text-sm font-bold ${posicao <= 3 ? 'bg-gradient-to-r from-yellow-400 to-orange-500 text-black' : 'bg-gray-500 text-white'}">
                    ${posicao}
                </span>
            </td>
            <td class="px-6 py-4">
                <div class="flex items-center">
                    <div class="flex-shrink-0 h-10 w-10">
                        <img class="h-10 w-10 rounded-full object-cover border-2 border-white/20" 
                             src="${jogador.foto_perfil || '/static/uploads/default-avatar.png'}" 
                             alt="${jogador.nickname}">
                    </div>
                    <div class="ml-4">
                        <div class="text-sm font-medium text-white">${jogador.nickname}</div>
                        <div class="text-sm text-white/60">${jogador.nome_real}</div>
                    </div>
                </div>
            </td>
            <td class="px-6 py-4 text-center text-sm text-green-400 font-medium">${jogador.vitorias}</td>
            <td class="px-6 py-4 text-center text-sm text-red-400 font-medium">${jogador.derrotas}</td>
            <td class="px-6 py-4 text-center text-sm text-white font-bold">${jogador.pontuacao}</td>
            <td class="px-6 py-4 text-center text-sm text-white">${percentualVitorias}%</td>
            <td class="px-6 py-4 text-sm ${premio ? premio.cor : 'text-white/60'}">
                ${premio ? premio.nome : '-'}
            </td>
        `;
        
        tbody.appendChild(row);
    });
}

// Função para criar histórico do amistoso
function criarHistoricoAmistoso(batalhas) {
    const historico = document.getElementById('historico-amistoso');
    historico.innerHTML = '';
    
    if (batalhas.length === 0) {
        historico.innerHTML = '<p class="text-white/60 text-center py-8">Nenhuma partida realizada ainda</p>';
        return;
    }
    
    batalhas.slice(0, 10).forEach(batalha => { // Mostrar apenas as 10 mais recentes
        const dataFormatada = new Date(batalha.created_at).toLocaleString('pt-BR');
        const vencedorNome = batalha.vencedor === 'A' ? batalha.jogador_a.nickname : batalha.jogador_b.nickname;
        
        const div = document.createElement('div');
        div.className = 'bg-white/5 rounded-lg p-4 border border-white/10';
        div.innerHTML = `
            <div class="flex justify-between items-center mb-2">
                <span class="text-white/60 text-sm">${dataFormatada}</span>
                <span class="text-green-400 font-bold">
                    <i class="fas fa-trophy mr-1"></i>
                    ${vencedorNome}
                </span>
            </div>
            
            <div class="flex items-center justify-between">
                <div class="text-white">
                    <span class="font-medium">${batalha.jogador_a.nickname}</span>
                    ${batalha.vencedor === 'A' ? '<i class="fas fa-crown text-yellow-400 ml-1"></i>' : ''}
                </div>
                <span class="text-white/60 font-bold mx-4">VS</span>
                <div class="text-white">
                    <span class="font-medium">${batalha.jogador_b.nickname}</span>
                    ${batalha.vencedor === 'B' ? '<i class="fas fa-crown text-yellow-400 ml-1"></i>' : ''}
                </div>
            </div>
            
            ${batalha.pokemon_destaque ? `
            <div class="mt-2 text-center">
                <span class="text-yellow-400 text-sm">
                    <i class="fas fa-star mr-1"></i>
                    Destaque: ${batalha.pokemon_destaque}
                </span>
            </div>
            ` : ''}
        `;
        
        historico.appendChild(div);
    });
}

// Função para exportar ranking
function exportarRankingAmistoso() {
    // Implementar exportação do ranking
    mostrarNotificacao('Funcionalidade de exportação em desenvolvimento!', 'info');
}

// Carregar dados ao carregar a página
document.addEventListener('DOMContentLoaded', carregarDadosAmistoso);

// Atualizar dados a cada 30 segundos
setInterval(carregarDadosAmistoso, 30000);
</script>
{% endblock %}
