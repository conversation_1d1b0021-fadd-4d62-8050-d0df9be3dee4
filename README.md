# 🏆 Torneio BAGREMON - Sistema de Gerenciamento

Uma aplicação web moderna para gerenciar o Torneio BAGREMON com detecção automática de modos (Amistoso/Oficial), upload de imagens local, vídeo de fundo e interface responsiva e animada.

## 🎯 Funcionalidades

### 🗓️ Detecção Automática de Modo
- ✅ **05/07/2025**: <PERSON><PERSON> (todos contra todos)
- ✅ **12/07/2025**: Modo Oficial (VGC com repescagem)
- ✅ Outras datas: Seleção manual do modo
- ✅ Interface adaptativa baseada no modo

### 👤 Gerenciamento de Jogadores
- ✅ Cadastro completo de jogadores (nome, nickname, tipos)
- ✅ **Upload de imagens local** (não mais URLs da internet)
- ✅ Gerenciamento de Pokémon por jogador
- ✅ Sistema de credenciamento para finais
- ✅ Fotos salvas na pasta `static/imagens/`

### ⚔️ Sistema de Batalhas
- ✅ Registro de batalhas VGC 2x2
- ✅ Seleção de Pokémon por batalha
- ✅ Sistema de Pokémon destaque
- ✅ Diferentes fases (classificatória, repescagem, semifinal, final)

### 📊 Classificação e Estatísticas
- ✅ Tabela classificatória em tempo real
- ✅ Sistema de pontuação (3 pontos por vitória)
- ✅ Estatísticas detalhadas por jogador
- ✅ Pokémon mais usado e destacado

### 🔄 Sistema de Repescagem
- ✅ Chave automática de repescagem (2º vs 7º, 3º vs 6º, 4º vs 5º)
- ✅ Trilha do Inferno para 8º colocado
- ✅ Visualização em árvore dos confrontos
- ✅ Semifinais e final

### 🎨 Interface Moderna
- ✅ **Vídeo de fundo**: `static/background/backgroundvideo.mp4`
- ✅ Design responsivo com TailwindCSS
- ✅ Animações suaves e efeitos visuais
- ✅ Título animado "Torneio BAGREMON"
- ✅ Paleta de cores temática Pokémon
- ✅ Ícones dos tipos Pokémon
- ✅ Cards interativos com hover effects

### 🏆 Modo Amistoso (05/07/2025)
- ✅ Todos os jogadores se enfrentam
- ✅ Ranking geral com pódio dos 3 primeiros
- ✅ Interface específica para amistoso
- ✅ Estatísticas detalhadas de partidas

### ⚔️ Modo Oficial (12/07/2025)
- ✅ Torneio VGC com batalhas 2x2
- ✅ Sistema de repescagem completo
- ✅ Trilha do Inferno para 8º colocado
- ✅ Credenciamento de até 8 Pokémon para finais

## 🚀 Instalação e Configuração

### Pré-requisitos
- Python 3.8 ou superior
- pip (gerenciador de pacotes Python)

### 1. Clone ou baixe o projeto
```bash
git clone <url-do-repositorio>
cd campeonato-pokemon-vgc
```

### 2. Instale as dependências
```bash
pip install -r requirements.txt
```

### 3. Execute a aplicação
```bash
python app.py
```

### 4. Acesse no navegador
Abra seu navegador e acesse: `http://localhost:5000`

## 📁 Estrutura do Projeto

```
campeonato-pokemon-vgc/
├── app.py                 # Aplicação Flask principal
├── models.py              # Modelos do banco de dados
├── requirements.txt       # Dependências Python
├── README.md             # Este arquivo
├── templates/            # Templates HTML
│   ├── base.html         # Template base
│   ├── index.html        # Página principal (classificação)
│   ├── jogadores.html    # Gerenciamento de jogadores
│   ├── batalhas.html     # Registro de batalhas
│   └── repescagem.html   # Chave de repescagem e finais
├── static/               # Arquivos estáticos
│   ├── css/
│   │   └── style.css     # Estilos customizados
│   ├── js/
│   │   └── app.js        # JavaScript customizado
│   └── uploads/          # Uploads de imagens
│       ├── jogadores/    # Fotos dos jogadores
│       └── pokemons/     # Imagens dos Pokémon
└── campeonato_pokemon.db # Banco de dados SQLite (criado automaticamente)
```

## 🎮 Como Usar

### 1. Cadastrar Jogadores
1. Acesse a aba "Jogadores"
2. Clique em "Novo Jogador"
3. Preencha os dados (nome, nickname, tipos)
4. Adicione uma foto de perfil (URL)
5. Salve o jogador

### 2. Adicionar Pokémon
1. Na lista de jogadores, clique em "Pokémon"
2. Preencha os dados do Pokémon
3. Marque se é credenciado para finais
4. Adicione à lista do jogador

### 3. Registrar Batalhas
1. Acesse a aba "Batalhas"
2. Clique em "Nova Batalha"
3. Selecione os jogadores A e B
4. Escolha 2 Pokémon para cada jogador
5. Defina o vencedor e Pokémon destaque
6. Registre a batalha

### 4. Acompanhar Classificação
- A classificação é atualizada automaticamente
- 3 pontos por vitória, 0 por derrota
- Critério de desempate: número de vitórias

### 5. Gerenciar Repescagem
1. Acesse a aba "Repescagem"
2. Clique em "Gerar Chave de Repescagem"
3. Acompanhe os confrontos
4. Registre as batalhas das fases eliminatórias

## 🏗️ Tecnologias Utilizadas

### Backend
- **Flask** - Framework web Python
- **SQLAlchemy** - ORM para banco de dados
- **SQLite** - Banco de dados leve

### Frontend
- **HTML5** - Estrutura das páginas
- **TailwindCSS** - Framework CSS utilitário
- **JavaScript** - Interatividade e AJAX
- **Font Awesome** - Ícones
- **Google Fonts** - Tipografia

### Recursos Visuais
- **Gradientes CSS** - Efeitos visuais
- **Animações CSS** - Transições suaves
- **Backdrop Blur** - Efeitos de vidro
- **Responsive Design** - Adaptável a todos os dispositivos

## 🎨 Personalização

### Cores dos Tipos Pokémon
As cores dos tipos são definidas no arquivo `static/css/style.css` e seguem o padrão oficial:
- Fire: #F08030
- Water: #6890F0
- Grass: #78C850
- Electric: #F8D030
- E mais...

### Adicionando Novos Tipos
Para adicionar novos tipos Pokémon:
1. Edite a lista `TIPOS_POKEMON` em `app.py`
2. Adicione a cor correspondente em `style.css`

## 🔧 API Endpoints

### Jogadores
- `GET /api/jogadores` - Listar jogadores
- `POST /api/jogadores` - Criar jogador
- `GET /api/jogadores/{id}` - Obter jogador
- `PUT /api/jogadores/{id}` - Atualizar jogador
- `DELETE /api/jogadores/{id}` - Excluir jogador

### Pokémon
- `GET /api/pokemons` - Listar Pokémon
- `POST /api/pokemons` - Criar Pokémon
- `DELETE /api/pokemons/{id}` - Excluir Pokémon

### Batalhas
- `GET /api/batalhas` - Listar batalhas
- `POST /api/batalhas` - Registrar batalha

### Classificação
- `GET /api/classificacao` - Obter classificação atual

## 🐛 Solução de Problemas

### Erro ao iniciar a aplicação
- Verifique se todas as dependências estão instaladas
- Certifique-se de estar usando Python 3.8+

### Banco de dados não criado
- O banco SQLite é criado automaticamente na primeira execução
- Verifique permissões de escrita na pasta do projeto

### Imagens não carregam
- Verifique se as URLs das imagens são válidas
- Certifique-se de que as pastas de upload existem

## 📝 Licença

Este projeto é de código aberto e está disponível sob a licença MIT.

## 🤝 Contribuições

Contribuições são bem-vindas! Sinta-se à vontade para:
- Reportar bugs
- Sugerir melhorias
- Enviar pull requests

## 📞 Suporte

Para dúvidas ou suporte, abra uma issue no repositório do projeto.

---

**Desenvolvido com ❤️ para a comunidade Pokémon VGC**
