from flask_sqlalchemy import SQLAlchemy
from datetime import datetime

db = SQLAlchemy()

class Jogador(db.Model):
    __tablename__ = 'jogadores'
    
    id = db.Column(db.Integer, primary_key=True)
    nome_real = db.Column(db.String(100), nullable=False)
    nickname = db.Column(db.String(50), nullable=False, unique=True)
    tipo_primario = db.Column(db.String(20), nullable=False)
    tipo_secundario = db.Column(db.String(20))
    foto_perfil = db.Column(db.String(200))
    vitorias = db.Column(db.Integer, default=0)
    derrotas = db.Column(db.Integer, default=0)
    pontuacao = db.Column(db.Integer, default=0)
    pokemon_destaque = db.Column(db.String(50))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # Relacionamentos
    pokemons = db.relationship('Pokemon', backref='treinador', lazy=True, cascade='all, delete-orphan')
    batalhas_como_a = db.relationship('Batalha', foreign_keys='Batalha.player_a_id', backref='jogador_a', lazy=True)
    batalhas_como_b = db.relationship('Batalha', foreign_keys='Batalha.player_b_id', backref='jogador_b', lazy=True)
    
    def to_dict(self):
        return {
            'id': self.id,
            'nome_real': self.nome_real,
            'nickname': self.nickname,
            'tipo_primario': self.tipo_primario,
            'tipo_secundario': self.tipo_secundario,
            'foto_perfil': self.foto_perfil,
            'vitorias': self.vitorias,
            'derrotas': self.derrotas,
            'pontuacao': self.pontuacao,
            'pokemon_destaque': self.pokemon_destaque,
            'pokemons': [p.to_dict() for p in self.pokemons]
        }
    
    def atualizar_estatisticas(self):
        """Atualiza vitórias, derrotas e pontuação baseado nas batalhas"""
        vitorias = 0
        derrotas = 0
        
        # Contar vitórias e derrotas
        for batalha in self.batalhas_como_a:
            if batalha.vencedor == 'A':
                vitorias += 1
            else:
                derrotas += 1
                
        for batalha in self.batalhas_como_b:
            if batalha.vencedor == 'B':
                vitorias += 1
            else:
                derrotas += 1
        
        self.vitorias = vitorias
        self.derrotas = derrotas
        self.pontuacao = vitorias * 3
        
        # Atualizar Pokémon destaque (mais citado)
        pokemon_count = {}
        for batalha in self.batalhas_como_a + self.batalhas_como_b:
            if batalha.pokemon_destaque:
                pokemon_count[batalha.pokemon_destaque] = pokemon_count.get(batalha.pokemon_destaque, 0) + 1
        
        if pokemon_count:
            self.pokemon_destaque = max(pokemon_count, key=pokemon_count.get)

class Pokemon(db.Model):
    __tablename__ = 'pokemons'
    
    id = db.Column(db.Integer, primary_key=True)
    nome = db.Column(db.String(50), nullable=False)
    tipo_primario = db.Column(db.String(20), nullable=False)
    tipo_secundario = db.Column(db.String(20))
    apelido = db.Column(db.String(50))
    imagem = db.Column(db.String(200))
    credenciado_finais = db.Column(db.Boolean, default=False)
    jogador_id = db.Column(db.Integer, db.ForeignKey('jogadores.id'), nullable=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'nome': self.nome,
            'tipo_primario': self.tipo_primario,
            'tipo_secundario': self.tipo_secundario,
            'apelido': self.apelido,
            'imagem': self.imagem,
            'credenciado_finais': self.credenciado_finais,
            'jogador_id': self.jogador_id
        }

class Batalha(db.Model):
    __tablename__ = 'batalhas'
    
    id = db.Column(db.Integer, primary_key=True)
    player_a_id = db.Column(db.Integer, db.ForeignKey('jogadores.id'), nullable=False)
    player_b_id = db.Column(db.Integer, db.ForeignKey('jogadores.id'), nullable=False)
    pokemon_a1 = db.Column(db.String(50), nullable=False)  # Pokémon 1 do jogador A
    pokemon_a2 = db.Column(db.String(50), nullable=False)  # Pokémon 2 do jogador A
    pokemon_b1 = db.Column(db.String(50), nullable=False)  # Pokémon 1 do jogador B
    pokemon_b2 = db.Column(db.String(50), nullable=False)  # Pokémon 2 do jogador B
    vencedor = db.Column(db.String(1), nullable=False)  # 'A' ou 'B'
    pokemon_destaque = db.Column(db.String(50))
    fase = db.Column(db.String(20), default='classificatoria')  # classificatoria, repescagem, semifinal, final
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'player_a_id': self.player_a_id,
            'player_b_id': self.player_b_id,
            'pokemon_a1': self.pokemon_a1,
            'pokemon_a2': self.pokemon_a2,
            'pokemon_b1': self.pokemon_b1,
            'pokemon_b2': self.pokemon_b2,
            'vencedor': self.vencedor,
            'pokemon_destaque': self.pokemon_destaque,
            'fase': self.fase,
            'created_at': self.created_at.isoformat() if self.created_at else None,
            'jogador_a': self.jogador_a.to_dict() if self.jogador_a else None,
            'jogador_b': self.jogador_b.to_dict() if self.jogador_b else None
        }

class Repescagem(db.Model):
    __tablename__ = 'repescagem'
    
    id = db.Column(db.Integer, primary_key=True)
    fase = db.Column(db.String(20), nullable=False)  # repescagem, trilha_inferno, semifinal, final
    confronto = db.Column(db.String(10))  # 2v7, 3v6, 4v5, etc
    player_a_id = db.Column(db.Integer, db.ForeignKey('jogadores.id'))
    player_b_id = db.Column(db.Integer, db.ForeignKey('jogadores.id'))
    vencedor_id = db.Column(db.Integer, db.ForeignKey('jogadores.id'))
    batalha_id = db.Column(db.Integer, db.ForeignKey('batalhas.id'))
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'fase': self.fase,
            'confronto': self.confronto,
            'player_a_id': self.player_a_id,
            'player_b_id': self.player_b_id,
            'vencedor_id': self.vencedor_id,
            'batalha_id': self.batalha_id
        }
