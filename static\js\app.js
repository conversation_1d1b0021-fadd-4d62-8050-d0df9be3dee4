// Funções globais para a aplicação Campeonato Pokémon VGC

// Função para obter a classe CSS do tipo Pokémon
function getTipoClass(tipo) {
    if (!tipo) return '';
    return 'tipo-' + tipo.toLowerCase();
}

// Função para formatar data
function formatarData(dataString) {
    const data = new Date(dataString);
    return data.toLocaleString('pt-BR', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    });
}

// Função para mostrar notificação
function mostrarNotificacao(mensagem, tipo = 'info') {
    const cores = {
        success: 'bg-green-500',
        error: 'bg-red-500',
        warning: 'bg-yellow-500',
        info: 'bg-blue-500'
    };
    
    const notificacao = document.createElement('div');
    notificacao.className = `fixed top-4 right-4 ${cores[tipo]} text-white px-6 py-3 rounded-lg shadow-lg z-50 fade-in`;
    notificacao.innerHTML = `
        <div class="flex items-center">
            <span>${mensagem}</span>
            <button onclick="this.parentElement.parentElement.remove()" class="ml-4 text-white hover:text-gray-200">
                <i class="fas fa-times"></i>
            </button>
        </div>
    `;
    
    document.body.appendChild(notificacao);
    
    // Remover automaticamente após 5 segundos
    setTimeout(() => {
        if (notificacao.parentElement) {
            notificacao.remove();
        }
    }, 5000);
}

// Função para validar formulários
function validarFormulario(formId, campos) {
    const form = document.getElementById(formId);
    let valido = true;
    
    campos.forEach(campo => {
        const elemento = document.getElementById(campo.id);
        const valor = elemento.value.trim();
        
        // Remover classes de erro anteriores
        elemento.classList.remove('border-red-500', 'ring-red-500');
        
        // Validar campo obrigatório
        if (campo.obrigatorio && !valor) {
            elemento.classList.add('border-red-500', 'ring-red-500');
            mostrarNotificacao(`O campo ${campo.nome} é obrigatório.`, 'error');
            valido = false;
        }
        
        // Validar tipo de campo
        if (valor && campo.tipo) {
            switch (campo.tipo) {
                case 'email':
                    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
                    if (!emailRegex.test(valor)) {
                        elemento.classList.add('border-red-500', 'ring-red-500');
                        mostrarNotificacao(`O campo ${campo.nome} deve ser um email válido.`, 'error');
                        valido = false;
                    }
                    break;
                    
                case 'url':
                    try {
                        new URL(valor);
                    } catch {
                        elemento.classList.add('border-red-500', 'ring-red-500');
                        mostrarNotificacao(`O campo ${campo.nome} deve ser uma URL válida.`, 'error');
                        valido = false;
                    }
                    break;
                    
                case 'numero':
                    if (isNaN(valor) || valor < 0) {
                        elemento.classList.add('border-red-500', 'ring-red-500');
                        mostrarNotificacao(`O campo ${campo.nome} deve ser um número válido.`, 'error');
                        valido = false;
                    }
                    break;
            }
        }
        
        // Validar tamanho mínimo
        if (valor && campo.minLength && valor.length < campo.minLength) {
            elemento.classList.add('border-red-500', 'ring-red-500');
            mostrarNotificacao(`O campo ${campo.nome} deve ter pelo menos ${campo.minLength} caracteres.`, 'error');
            valido = false;
        }
        
        // Validar tamanho máximo
        if (valor && campo.maxLength && valor.length > campo.maxLength) {
            elemento.classList.add('border-red-500', 'ring-red-500');
            mostrarNotificacao(`O campo ${campo.nome} deve ter no máximo ${campo.maxLength} caracteres.`, 'error');
            valido = false;
        }
    });
    
    return valido;
}

// Função para fazer requisições HTTP com tratamento de erro
async function fazerRequisicao(url, opcoes = {}) {
    try {
        const response = await fetch(url, {
            headers: {
                'Content-Type': 'application/json',
                ...opcoes.headers
            },
            ...opcoes
        });
        
        if (!response.ok) {
            const errorData = await response.json().catch(() => ({}));
            throw new Error(errorData.error || `Erro HTTP: ${response.status}`);
        }
        
        return await response.json();
    } catch (error) {
        console.error('Erro na requisição:', error);
        mostrarNotificacao(error.message || 'Erro na comunicação com o servidor.', 'error');
        throw error;
    }
}

// Função para debounce (evitar múltiplas chamadas rápidas)
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Função para confirmar ações perigosas
function confirmarAcao(mensagem, callback) {
    const modal = document.createElement('div');
    modal.className = 'fixed inset-0 bg-black/50 backdrop-blur-sm z-50 flex items-center justify-center';
    modal.innerHTML = `
        <div class="bg-white rounded-lg p-6 max-w-md w-full mx-4">
            <div class="flex items-center mb-4">
                <i class="fas fa-exclamation-triangle text-yellow-500 text-2xl mr-3"></i>
                <h3 class="text-lg font-semibold text-gray-800">Confirmação</h3>
            </div>
            <p class="text-gray-600 mb-6">${mensagem}</p>
            <div class="flex justify-end space-x-3">
                <button onclick="this.closest('.fixed').remove()" 
                        class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                    Cancelar
                </button>
                <button onclick="this.closest('.fixed').remove(); (${callback})()" 
                        class="px-4 py-2 bg-red-500 text-white rounded-md hover:bg-red-600">
                    Confirmar
                </button>
            </div>
        </div>
    `;
    
    document.body.appendChild(modal);
}

// Função para loading em botões
function toggleLoadingButton(buttonId, loading = true) {
    const button = document.getElementById(buttonId);
    if (!button) return;
    
    if (loading) {
        button.disabled = true;
        button.classList.add('loading');
        button.innerHTML = button.innerHTML.replace(/(<i[^>]*><\/i>\s*)?(.*)/, '$1Carregando...');
    } else {
        button.disabled = false;
        button.classList.remove('loading');
    }
}

// Função para animar elementos quando entram na tela
function observarElementos() {
    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('fade-in');
            }
        });
    }, {
        threshold: 0.1
    });
    
    document.querySelectorAll('.card-hover, .bg-white\\/10').forEach(el => {
        observer.observe(el);
    });
}

// Função para filtrar listas
function filtrarLista(inputId, containerId, seletor) {
    const input = document.getElementById(inputId);
    const container = document.getElementById(containerId);
    
    if (!input || !container) return;
    
    input.addEventListener('input', debounce((e) => {
        const filtro = e.target.value.toLowerCase();
        const items = container.querySelectorAll(seletor);
        
        items.forEach(item => {
            const texto = item.textContent.toLowerCase();
            if (texto.includes(filtro)) {
                item.style.display = '';
                item.classList.add('fade-in');
            } else {
                item.style.display = 'none';
            }
        });
    }, 300));
}

// Função para copiar texto para clipboard
async function copiarTexto(texto) {
    try {
        await navigator.clipboard.writeText(texto);
        mostrarNotificacao('Texto copiado para a área de transferência!', 'success');
    } catch (error) {
        console.error('Erro ao copiar texto:', error);
        mostrarNotificacao('Erro ao copiar texto.', 'error');
    }
}

// Função para exportar dados como JSON
function exportarDados(dados, nomeArquivo) {
    const dataStr = JSON.stringify(dados, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });
    
    const link = document.createElement('a');
    link.href = URL.createObjectURL(dataBlob);
    link.download = nomeArquivo;
    link.click();
    
    mostrarNotificacao('Dados exportados com sucesso!', 'success');
}

// Função para preview de imagem
function previewImagem(inputId, previewId) {
    const input = document.getElementById(inputId);
    const preview = document.getElementById(previewId);
    
    if (!input || !preview) return;
    
    input.addEventListener('change', (e) => {
        const file = e.target.files[0];
        if (file) {
            const reader = new FileReader();
            reader.onload = (e) => {
                preview.src = e.target.result;
                preview.style.display = 'block';
            };
            reader.readAsDataURL(file);
        }
    });
}

// Função para validar URL de imagem
function validarUrlImagem(url) {
    return new Promise((resolve) => {
        const img = new Image();
        img.onload = () => resolve(true);
        img.onerror = () => resolve(false);
        img.src = url;
    });
}

// Função para formatar números
function formatarNumero(numero, casasDecimais = 0) {
    return new Intl.NumberFormat('pt-BR', {
        minimumFractionDigits: casasDecimais,
        maximumFractionDigits: casasDecimais
    }).format(numero);
}

// Função para calcular estatísticas
function calcularEstatisticas(jogadores) {
    const stats = {
        totalJogadores: jogadores.length,
        totalBatalhas: 0,
        totalVitorias: 0,
        totalDerrotas: 0,
        mediaVitorias: 0,
        mediaDerrotas: 0,
        jogadorMaisVitorias: null,
        jogadorMaisPontos: null
    };
    
    if (jogadores.length === 0) return stats;
    
    jogadores.forEach(jogador => {
        stats.totalVitorias += jogador.vitorias || 0;
        stats.totalDerrotas += jogador.derrotas || 0;
        
        if (!stats.jogadorMaisVitorias || jogador.vitorias > stats.jogadorMaisVitorias.vitorias) {
            stats.jogadorMaisVitorias = jogador;
        }
        
        if (!stats.jogadorMaisPontos || jogador.pontuacao > stats.jogadorMaisPontos.pontuacao) {
            stats.jogadorMaisPontos = jogador;
        }
    });
    
    stats.totalBatalhas = stats.totalVitorias; // Cada vitória representa uma batalha
    stats.mediaVitorias = stats.totalVitorias / jogadores.length;
    stats.mediaDerrotas = stats.totalDerrotas / jogadores.length;
    
    return stats;
}

// Inicializar funcionalidades globais quando o DOM estiver pronto
document.addEventListener('DOMContentLoaded', () => {
    // Observar elementos para animações
    observarElementos();
    
    // Adicionar efeitos de hover aos cards
    document.querySelectorAll('.card-hover').forEach(card => {
        card.addEventListener('mouseenter', () => {
            card.style.transform = 'translateY(-8px) scale(1.02)';
        });
        
        card.addEventListener('mouseleave', () => {
            card.style.transform = 'translateY(0) scale(1)';
        });
    });
    
    // Fechar modais ao clicar fora
    document.addEventListener('click', (e) => {
        if (e.target.classList.contains('fixed') && e.target.classList.contains('inset-0')) {
            const modal = e.target;
            if (modal.id && modal.id.includes('modal')) {
                modal.classList.add('hidden');
            }
        }
    });
    
    // Atalhos de teclado
    document.addEventListener('keydown', (e) => {
        // ESC para fechar modais
        if (e.key === 'Escape') {
            document.querySelectorAll('.fixed.inset-0:not(.hidden)').forEach(modal => {
                modal.classList.add('hidden');
            });
        }
    });
});

// Exportar funções para uso global
window.PokemonVGC = {
    getTipoClass,
    formatarData,
    mostrarNotificacao,
    validarFormulario,
    fazerRequisicao,
    debounce,
    confirmarAcao,
    toggleLoadingButton,
    filtrarLista,
    copiarTexto,
    exportarDados,
    previewImagem,
    validarUrlImagem,
    formatarNumero,
    calcularEstatisticas
};
