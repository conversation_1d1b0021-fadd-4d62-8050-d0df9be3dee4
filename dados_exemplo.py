#!/usr/bin/env python3
"""
Script para popular o banco de dados com dados de exemplo
Execute: python dados_exemplo.py
"""

import requests
import json

BASE_URL = "http://localhost:5000/api"

# Dados de exemplo dos jogadores
jogadores_exemplo = [
    {
        "nome_real": "<PERSON>",
        "nickname": "DragonMaster",
        "tipo_primario": "Dragon",
        "tipo_secundario": "Fire",
        "foto_perfil": "https://i.imgur.com/placeholder1.jpg"
    },
    {
        "nome_real": "<PERSON>",
        "nickname": "Aqua<PERSON><PERSON><PERSON>",
        "tipo_primario": "Water",
        "tipo_secundario": "Ice",
        "foto_perfil": "https://i.imgur.com/placeholder2.jpg"
    },
    {
        "nome_real": "Pedro Costa",
        "nickname": "ThunderBolt",
        "tipo_primario": "Electric",
        "tipo_secundario": None,
        "foto_perfil": "https://i.imgur.com/placeholder3.jpg"
    },
    {
        "nome_real": "<PERSON>",
        "nickname": "NatureSpirit",
        "tipo_primario": "<PERSON>",
        "tipo_secundario": "Fairy",
        "foto_perfil": "https://i.imgur.com/placeholder4.jpg"
    },
    {
        "nome_real": "Carlos Ferreira",
        "nickname": "SteelGuard",
        "tipo_primario": "Steel",
        "tipo_secundario": "Rock",
        "foto_perfil": "https://i.imgur.com/placeholder5.jpg"
    },
    {
        "nome_real": "Lucia Mendes",
        "nickname": "PsychicMind",
        "tipo_primario": "Psychic",
        "tipo_secundario": "Ghost",
        "foto_perfil": "https://i.imgur.com/placeholder6.jpg"
    },
    {
        "nome_real": "Roberto Lima",
        "nickname": "FightClub",
        "tipo_primario": "Fighting",
        "tipo_secundario": "Dark",
        "foto_perfil": "https://i.imgur.com/placeholder7.jpg"
    },
    {
        "nome_real": "Fernanda Rocha",
        "nickname": "BugCatcher",
        "tipo_primario": "Bug",
        "tipo_secundario": "Flying",
        "foto_perfil": "https://i.imgur.com/placeholder8.jpg"
    }
]

# Pokémon de exemplo para cada jogador
pokemons_exemplo = {
    "DragonMaster": [
        {"nome": "Garchomp", "tipo_primario": "Dragon", "tipo_secundario": "Ground", "apelido": "Chomper"},
        {"nome": "Salamence", "tipo_primario": "Dragon", "tipo_secundario": "Flying", "apelido": "Wings"},
        {"nome": "Charizard", "tipo_primario": "Fire", "tipo_secundario": "Flying", "apelido": "Blaze"},
        {"nome": "Dragonite", "tipo_primario": "Dragon", "tipo_secundario": "Flying", "apelido": "Gentle"},
    ],
    "AquaQueen": [
        {"nome": "Gyarados", "tipo_primario": "Water", "tipo_secundario": "Flying", "apelido": "Tsunami"},
        {"nome": "Lapras", "tipo_primario": "Water", "tipo_secundario": "Ice", "apelido": "Gentle"},
        {"nome": "Vaporeon", "tipo_primario": "Water", "tipo_secundario": None, "apelido": "Aqua"},
        {"nome": "Milotic", "tipo_primario": "Water", "tipo_secundario": None, "apelido": "Beauty"},
    ],
    "ThunderBolt": [
        {"nome": "Pikachu", "tipo_primario": "Electric", "tipo_secundario": None, "apelido": "Sparky"},
        {"nome": "Raichu", "tipo_primario": "Electric", "tipo_secundario": None, "apelido": "Thunder"},
        {"nome": "Magnezone", "tipo_primario": "Electric", "tipo_secundario": "Steel", "apelido": "Magnet"},
        {"nome": "Jolteon", "tipo_primario": "Electric", "tipo_secundario": None, "apelido": "Volt"},
    ],
    "NatureSpirit": [
        {"nome": "Venusaur", "tipo_primario": "Grass", "tipo_secundario": "Poison", "apelido": "Ivy"},
        {"nome": "Leafeon", "tipo_primario": "Grass", "tipo_secundario": None, "apelido": "Leaf"},
        {"nome": "Roserade", "tipo_primario": "Grass", "tipo_secundario": "Poison", "apelido": "Rose"},
        {"nome": "Togekiss", "tipo_primario": "Fairy", "tipo_secundario": "Flying", "apelido": "Angel"},
    ],
    "SteelGuard": [
        {"nome": "Metagross", "tipo_primario": "Steel", "tipo_secundario": "Psychic", "apelido": "Meta"},
        {"nome": "Aggron", "tipo_primario": "Steel", "tipo_secundario": "Rock", "apelido": "Tank"},
        {"nome": "Lucario", "tipo_primario": "Fighting", "tipo_secundario": "Steel", "apelido": "Aura"},
        {"nome": "Skarmory", "tipo_primario": "Steel", "tipo_secundario": "Flying", "apelido": "Armor"},
    ],
    "PsychicMind": [
        {"nome": "Alakazam", "tipo_primario": "Psychic", "tipo_secundario": None, "apelido": "Mystic"},
        {"nome": "Gengar", "tipo_primario": "Ghost", "tipo_secundario": "Poison", "apelido": "Shadow"},
        {"nome": "Espeon", "tipo_primario": "Psychic", "tipo_secundario": None, "apelido": "Psyche"},
        {"nome": "Gardevoir", "tipo_primario": "Psychic", "tipo_secundario": "Fairy", "apelido": "Grace"},
    ],
    "FightClub": [
        {"nome": "Machamp", "tipo_primario": "Fighting", "tipo_secundario": None, "apelido": "Muscle"},
        {"nome": "Lucario", "tipo_primario": "Fighting", "tipo_secundario": "Steel", "apelido": "Fighter"},
        {"nome": "Umbreon", "tipo_primario": "Dark", "tipo_secundario": None, "apelido": "Dark"},
        {"nome": "Tyranitar", "tipo_primario": "Rock", "tipo_secundario": "Dark", "apelido": "Titan"},
    ],
    "BugCatcher": [
        {"nome": "Scizor", "tipo_primario": "Bug", "tipo_secundario": "Steel", "apelido": "Scissors"},
        {"nome": "Heracross", "tipo_primario": "Bug", "tipo_secundario": "Fighting", "apelido": "Hercules"},
        {"nome": "Crobat", "tipo_primario": "Poison", "tipo_secundario": "Flying", "apelido": "Speedy"},
        {"nome": "Volcarona", "tipo_primario": "Bug", "tipo_secundario": "Fire", "apelido": "Moth"},
    ]
}

def criar_jogadores():
    """Criar jogadores de exemplo"""
    print("Criando jogadores de exemplo...")
    jogadores_criados = {}
    
    for jogador in jogadores_exemplo:
        try:
            response = requests.post(f"{BASE_URL}/jogadores", json=jogador)
            if response.status_code == 201:
                jogador_criado = response.json()
                jogadores_criados[jogador["nickname"]] = jogador_criado["id"]
                print(f"✅ Jogador {jogador['nickname']} criado com ID {jogador_criado['id']}")
            else:
                print(f"❌ Erro ao criar jogador {jogador['nickname']}: {response.text}")
        except Exception as e:
            print(f"❌ Erro ao criar jogador {jogador['nickname']}: {e}")
    
    return jogadores_criados

def criar_pokemons(jogadores_criados):
    """Criar Pokémon para cada jogador"""
    print("\nCriando Pokémon para os jogadores...")
    
    for nickname, jogador_id in jogadores_criados.items():
        if nickname in pokemons_exemplo:
            for pokemon in pokemons_exemplo[nickname]:
                pokemon_data = {
                    **pokemon,
                    "jogador_id": jogador_id,
                    "credenciado_finais": True  # Todos credenciados para exemplo
                }
                
                try:
                    response = requests.post(f"{BASE_URL}/pokemons", json=pokemon_data)
                    if response.status_code == 201:
                        print(f"✅ Pokémon {pokemon['nome']} criado para {nickname}")
                    else:
                        print(f"❌ Erro ao criar Pokémon {pokemon['nome']}: {response.text}")
                except Exception as e:
                    print(f"❌ Erro ao criar Pokémon {pokemon['nome']}: {e}")

def criar_batalhas_exemplo(jogadores_criados):
    """Criar algumas batalhas de exemplo"""
    print("\nCriando batalhas de exemplo...")
    
    # Lista de nicknames para facilitar
    nicknames = list(jogadores_criados.keys())
    
    # Batalhas de exemplo
    batalhas = [
        {
            "player_a": "DragonMaster",
            "player_b": "AquaQueen",
            "pokemon_a1": "Garchomp",
            "pokemon_a2": "Charizard",
            "pokemon_b1": "Gyarados",
            "pokemon_b2": "Lapras",
            "vencedor": "A",
            "pokemon_destaque": "Garchomp"
        },
        {
            "player_a": "ThunderBolt",
            "player_b": "NatureSpirit",
            "pokemon_a1": "Pikachu",
            "pokemon_a2": "Raichu",
            "pokemon_b1": "Venusaur",
            "pokemon_b2": "Leafeon",
            "vencedor": "B",
            "pokemon_destaque": "Venusaur"
        },
        {
            "player_a": "SteelGuard",
            "player_b": "PsychicMind",
            "pokemon_a1": "Metagross",
            "pokemon_a2": "Aggron",
            "pokemon_b1": "Alakazam",
            "pokemon_b2": "Gengar",
            "vencedor": "A",
            "pokemon_destaque": "Metagross"
        },
        {
            "player_a": "FightClub",
            "player_b": "BugCatcher",
            "pokemon_a1": "Machamp",
            "pokemon_a2": "Lucario",
            "pokemon_b1": "Scizor",
            "pokemon_b2": "Heracross",
            "vencedor": "B",
            "pokemon_destaque": "Scizor"
        }
    ]
    
    for batalha in batalhas:
        batalha_data = {
            "player_a_id": jogadores_criados[batalha["player_a"]],
            "player_b_id": jogadores_criados[batalha["player_b"]],
            "pokemon_a1": batalha["pokemon_a1"],
            "pokemon_a2": batalha["pokemon_a2"],
            "pokemon_b1": batalha["pokemon_b1"],
            "pokemon_b2": batalha["pokemon_b2"],
            "vencedor": batalha["vencedor"],
            "pokemon_destaque": batalha["pokemon_destaque"],
            "fase": "classificatoria"
        }
        
        try:
            response = requests.post(f"{BASE_URL}/batalhas", json=batalha_data)
            if response.status_code == 201:
                print(f"✅ Batalha {batalha['player_a']} vs {batalha['player_b']} criada")
            else:
                print(f"❌ Erro ao criar batalha: {response.text}")
        except Exception as e:
            print(f"❌ Erro ao criar batalha: {e}")

def main():
    """Função principal"""
    print("🏆 Populando banco de dados com dados de exemplo...")
    print("⚠️  Certifique-se de que a aplicação está rodando em http://localhost:5000")
    
    try:
        # Testar conexão
        response = requests.get(f"{BASE_URL}/jogadores")
        if response.status_code != 200:
            print("❌ Erro: Não foi possível conectar à API. Verifique se a aplicação está rodando.")
            return
        
        # Criar dados de exemplo
        jogadores_criados = criar_jogadores()
        
        if jogadores_criados:
            criar_pokemons(jogadores_criados)
            criar_batalhas_exemplo(jogadores_criados)
            
            print("\n🎉 Dados de exemplo criados com sucesso!")
            print("🌐 Acesse http://localhost:5000 para ver a aplicação")
        else:
            print("❌ Nenhum jogador foi criado. Verifique os erros acima.")
            
    except requests.exceptions.ConnectionError:
        print("❌ Erro: Não foi possível conectar à aplicação.")
        print("   Certifique-se de que está rodando: python app.py")
    except Exception as e:
        print(f"❌ Erro inesperado: {e}")

if __name__ == "__main__":
    main()
