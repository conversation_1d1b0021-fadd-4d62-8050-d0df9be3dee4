{% extends "base.html" %}

{% block title %}Repescagem e Finais - Campeonato Pokémon VGC{% endblock %}

{% block content %}
<div class="space-y-8">
    <!-- Header -->
    <div class="text-center">
        <h2 class="text-4xl font-bold text-white mb-2">
            <i class="fas fa-sitemap text-purple-400 mr-3"></i>
            Repescagem e Finais
        </h2>
        <p class="text-white/80 text-lg">Acompanhe a chave eliminatória do campeonato</p>
    </div>

    <!-- Status do Campeonato -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg p-6">
        <h3 class="text-xl font-semibold text-white mb-4">
            <i class="fas fa-info-circle mr-2"></i>
            Status do Campeonato
        </h3>
        
        <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div class="text-center">
                <div class="text-2xl font-bold text-green-400" id="fase-atual">Classificatória</div>
                <div class="text-white/70 text-sm">Fase Atual</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-blue-400" id="classificados">0/4</div>
                <div class="text-white/70 text-sm">Classificados para Semifinal</div>
            </div>
            <div class="text-center">
                <div class="text-2xl font-bold text-yellow-400" id="campeao">-</div>
                <div class="text-white/70 text-sm">Campeão</div>
            </div>
        </div>
    </div>

    <!-- Classificação Final da Fase Classificatória -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-white/20">
            <h3 class="text-xl font-semibold text-white">
                <i class="fas fa-list-ol mr-2"></i>
                Classificação Final - Fase Classificatória
            </h3>
        </div>
        
        <div class="p-6">
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4" id="classificacao-final">
                <!-- Será preenchido via JavaScript -->
            </div>
        </div>
    </div>

    <!-- Chave de Repescagem -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-white/20">
            <h3 class="text-xl font-semibold text-white">
                <i class="fas fa-tournament mr-2"></i>
                Chave de Repescagem
            </h3>
        </div>
        
        <div class="p-6">
            <div class="space-y-8">
                <!-- Confrontos da Repescagem -->
                <div>
                    <h4 class="text-lg font-semibold text-white mb-4">Confrontos da Repescagem</h4>
                    <div class="grid grid-cols-1 md:grid-cols-3 gap-6" id="confrontos-repescagem">
                        <!-- Será preenchido via JavaScript -->
                    </div>
                </div>
                
                <!-- Trilha do Inferno (8º lugar) -->
                <div>
                    <h4 class="text-lg font-semibold text-red-400 mb-4">
                        <i class="fas fa-fire mr-2"></i>
                        Trilha do Inferno (8º Lugar)
                    </h4>
                    <div class="bg-red-500/20 rounded-lg p-4">
                        <div id="trilha-inferno">
                            <!-- Será preenchido via JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Semifinais e Final -->
    <div class="bg-white/10 backdrop-blur-md rounded-lg overflow-hidden">
        <div class="px-6 py-4 border-b border-white/20">
            <h3 class="text-xl font-semibold text-white">
                <i class="fas fa-trophy mr-2"></i>
                Semifinais e Final
            </h3>
        </div>
        
        <div class="p-6">
            <div class="space-y-8">
                <!-- Semifinais -->
                <div>
                    <h4 class="text-lg font-semibold text-white mb-4">Semifinais</h4>
                    <div class="grid grid-cols-1 md:grid-cols-2 gap-6" id="semifinais">
                        <!-- Será preenchido via JavaScript -->
                    </div>
                </div>
                
                <!-- Final -->
                <div>
                    <h4 class="text-lg font-semibold text-yellow-400 mb-4">
                        <i class="fas fa-crown mr-2"></i>
                        Grande Final
                    </h4>
                    <div class="flex justify-center">
                        <div id="final" class="w-full max-w-md">
                            <!-- Será preenchido via JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Botões de Ação -->
    <div class="text-center space-x-4">
        <button onclick="gerarChaveRepescagem()" 
                class="bg-purple-500 hover:bg-purple-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
            <i class="fas fa-cogs mr-2"></i>
            Gerar Chave de Repescagem
        </button>
        
        <button onclick="atualizarChave()" 
                class="bg-blue-500 hover:bg-blue-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
            <i class="fas fa-sync-alt mr-2"></i>
            Atualizar Chave
        </button>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let classificacaoFinal = [];

// Função para carregar classificação final
async function carregarClassificacaoFinal() {
    try {
        const response = await fetch('/api/classificacao');
        classificacaoFinal = await response.json();
        
        const container = document.getElementById('classificacao-final');
        container.innerHTML = '';
        
        classificacaoFinal.forEach((jogador, index) => {
            const posicao = index + 1;
            let statusClass = '';
            let statusText = '';
            
            if (posicao === 1) {
                statusClass = 'border-yellow-400 bg-yellow-400/20';
                statusText = 'Direto para Semifinal';
            } else if (posicao >= 2 && posicao <= 7) {
                statusClass = 'border-blue-400 bg-blue-400/20';
                statusText = 'Repescagem';
            } else {
                statusClass = 'border-red-400 bg-red-400/20';
                statusText = 'Trilha do Inferno';
            }
            
            const card = document.createElement('div');
            card.className = `border-2 ${statusClass} rounded-lg p-4`;
            card.innerHTML = `
                <div class="text-center">
                    <div class="text-2xl font-bold text-white mb-2">${posicao}º</div>
                    <img class="h-16 w-16 rounded-full mx-auto object-cover border-2 border-white/20 mb-2" 
                         src="${jogador.foto_perfil || '/static/uploads/default-avatar.png'}" 
                         alt="${jogador.nickname}">
                    <h4 class="text-white font-bold">${jogador.nickname}</h4>
                    <p class="text-white/60 text-sm">${jogador.nome_real}</p>
                    <div class="mt-2">
                        <span class="text-yellow-400 font-bold">${jogador.pontuacao} pts</span>
                        <span class="text-white/60 text-sm ml-2">(${jogador.vitorias}V-${jogador.derrotas}D)</span>
                    </div>
                    <div class="mt-2 text-xs text-white/80">${statusText}</div>
                </div>
            `;
            container.appendChild(card);
        });
        
    } catch (error) {
        console.error('Erro ao carregar classificação:', error);
        alert('Erro ao carregar classificação final.');
    }
}

// Função para gerar chave de repescagem
function gerarChaveRepescagem() {
    if (classificacaoFinal.length < 8) {
        alert('É necessário ter 8 jogadores para gerar a chave de repescagem.');
        return;
    }
    
    // Confrontos da repescagem: 2º vs 7º, 3º vs 6º, 4º vs 5º
    const confrontos = [
        { nome: '2º vs 7º', jogadorA: classificacaoFinal[1], jogadorB: classificacaoFinal[6] },
        { nome: '3º vs 6º', jogadorA: classificacaoFinal[2], jogadorB: classificacaoFinal[5] },
        { nome: '4º vs 5º', jogadorA: classificacaoFinal[3], jogadorB: classificacaoFinal[4] }
    ];
    
    const container = document.getElementById('confrontos-repescagem');
    container.innerHTML = '';
    
    confrontos.forEach(confronto => {
        const card = document.createElement('div');
        card.className = 'bg-white/5 rounded-lg p-4 border border-white/10';
        card.innerHTML = `
            <h5 class="text-center text-white font-bold mb-4">${confronto.nome}</h5>
            
            <div class="space-y-4">
                <!-- Jogador A -->
                <div class="flex items-center justify-between p-3 bg-blue-500/20 rounded">
                    <div class="flex items-center space-x-3">
                        <img class="h-10 w-10 rounded-full object-cover" 
                             src="${confronto.jogadorA.foto_perfil || '/static/uploads/default-avatar.png'}" 
                             alt="${confronto.jogadorA.nickname}">
                        <div>
                            <div class="text-white font-medium">${confronto.jogadorA.nickname}</div>
                            <div class="text-white/60 text-sm">${confronto.jogadorA.pontuacao} pts</div>
                        </div>
                    </div>
                </div>
                
                <div class="text-center text-white/60 font-bold">VS</div>
                
                <!-- Jogador B -->
                <div class="flex items-center justify-between p-3 bg-red-500/20 rounded">
                    <div class="flex items-center space-x-3">
                        <img class="h-10 w-10 rounded-full object-cover" 
                             src="${confronto.jogadorB.foto_perfil || '/static/uploads/default-avatar.png'}" 
                             alt="${confronto.jogadorB.nickname}">
                        <div>
                            <div class="text-white font-medium">${confronto.jogadorB.nickname}</div>
                            <div class="text-white/60 text-sm">${confronto.jogadorB.pontuacao} pts</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="mt-4 text-center">
                <div class="text-white/60 text-sm">Vencedor:</div>
                <div class="text-yellow-400 font-bold">A definir</div>
            </div>
        `;
        container.appendChild(card);
    });
    
    // Trilha do Inferno (8º lugar)
    const trilhaContainer = document.getElementById('trilha-inferno');
    const oitavoColocado = classificacaoFinal[7];
    
    trilhaContainer.innerHTML = `
        <div class="text-center mb-4">
            <h5 class="text-white font-bold mb-2">Desafiante da Trilha do Inferno</h5>
            <div class="inline-flex items-center space-x-3 p-3 bg-red-600/30 rounded-lg">
                <img class="h-12 w-12 rounded-full object-cover" 
                     src="${oitavoColocado.foto_perfil || '/static/uploads/default-avatar.png'}" 
                     alt="${oitavoColocado.nickname}">
                <div>
                    <div class="text-white font-medium">${oitavoColocado.nickname}</div>
                    <div class="text-white/60 text-sm">${oitavoColocado.pontuacao} pts</div>
                </div>
            </div>
        </div>
        
        <div class="space-y-3 text-white/80 text-sm">
            <div class="flex items-center">
                <i class="fas fa-arrow-right text-red-400 mr-2"></i>
                <span>1ª Batalha: vs Perdedor de 4º vs 5º</span>
            </div>
            <div class="flex items-center">
                <i class="fas fa-arrow-right text-red-400 mr-2"></i>
                <span>2ª Batalha: vs Perdedor de 3º vs 6º (se vencer a 1ª)</span>
            </div>
            <div class="flex items-center">
                <i class="fas fa-crown text-yellow-400 mr-2"></i>
                <span>Se vencer ambas: Vaga na Semifinal como Wildcard</span>
            </div>
        </div>
    `;
    
    alert('Chave de repescagem gerada com sucesso!');
}

// Função para criar card de confronto vazio
function criarCardConfronto(titulo, subtitulo = '') {
    return `
        <div class="bg-white/5 rounded-lg p-4 border border-white/10">
            <h5 class="text-center text-white font-bold mb-4">${titulo}</h5>
            ${subtitulo ? `<p class="text-center text-white/60 text-sm mb-4">${subtitulo}</p>` : ''}
            
            <div class="space-y-4">
                <div class="p-3 bg-gray-500/20 rounded text-center">
                    <div class="text-white/60">Aguardando classificação</div>
                </div>
                
                <div class="text-center text-white/60 font-bold">VS</div>
                
                <div class="p-3 bg-gray-500/20 rounded text-center">
                    <div class="text-white/60">Aguardando classificação</div>
                </div>
            </div>
            
            <div class="mt-4 text-center">
                <div class="text-white/60 text-sm">Vencedor:</div>
                <div class="text-yellow-400 font-bold">A definir</div>
            </div>
        </div>
    `;
}

// Função para atualizar chave
function atualizarChave() {
    carregarClassificacaoFinal();
    
    // Inicializar semifinais vazias
    const semifinaisContainer = document.getElementById('semifinais');
    semifinaisContainer.innerHTML = `
        ${criarCardConfronto('Semifinal 1', '1º Colocado vs Classificado da Repescagem')}
        ${criarCardConfronto('Semifinal 2', 'Classificados da Repescagem')}
    `;
    
    // Inicializar final vazia
    const finalContainer = document.getElementById('final');
    finalContainer.innerHTML = criarCardConfronto('Grande Final', 'Vencedores das Semifinais');
}

// Carregar dados ao carregar a página
document.addEventListener('DOMContentLoaded', () => {
    carregarClassificacaoFinal();
    atualizarChave();
});
</script>
{% endblock %}
