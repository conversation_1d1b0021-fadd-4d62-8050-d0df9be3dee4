{% extends "base.html" %}

{% block title %}Selecionar Modo - Torneio BAGREMON{% endblock %}

{% block content %}
<div class="min-h-screen flex items-center justify-center">
    <div class="bg-white/10 backdrop-blur-md rounded-lg p-8 max-w-md w-full mx-4">
        <div class="text-center mb-8">
            <h2 class="text-3xl font-bold text-white mb-4">
                <i class="fas fa-calendar-alt text-yellow-400 mr-3"></i>
                Selecionar Modo do Torneio
            </h2>
            <p class="text-white/80">Escolha o modo do torneio para hoje:</p>
            <p class="text-yellow-300 font-semibold mt-2" id="data-atual"></p>
        </div>

        <div class="space-y-4">
            <!-- Modo Amistoso -->
            <a href="/modo/amistoso" 
               class="block w-full bg-green-500/20 hover:bg-green-500/30 border-2 border-green-400 rounded-lg p-6 transition-all duration-300 hover:scale-105">
                <div class="text-center">
                    <i class="fas fa-handshake text-green-400 text-3xl mb-3"></i>
                    <h3 class="text-xl font-bold text-white mb-2">Modo Amistoso</h3>
                    <p class="text-white/80 text-sm">
                        Todos contra todos<br>
                        Ranking geral com premiação para os 3 primeiros
                    </p>
                    <div class="mt-3 text-green-300 text-sm font-medium">
                        📅 Data oficial: 05/07/2025
                    </div>
                </div>
            </a>

            <!-- Modo Oficial -->
            <a href="/modo/oficial" 
               class="block w-full bg-red-500/20 hover:bg-red-500/30 border-2 border-red-400 rounded-lg p-6 transition-all duration-300 hover:scale-105">
                <div class="text-center">
                    <i class="fas fa-trophy text-red-400 text-3xl mb-3"></i>
                    <h3 class="text-xl font-bold text-white mb-2">Modo Oficial</h3>
                    <p class="text-white/80 text-sm">
                        Torneio VGC com repescagem<br>
                        Batalhas 2x2 com sistema eliminatório
                    </p>
                    <div class="mt-3 text-red-300 text-sm font-medium">
                        📅 Data oficial: 12/07/2025
                    </div>
                </div>
            </a>
        </div>

        <div class="mt-8 text-center">
            <p class="text-white/60 text-sm">
                <i class="fas fa-info-circle mr-1"></i>
                O modo será detectado automaticamente nas datas oficiais
            </p>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
// Mostrar data atual
document.addEventListener('DOMContentLoaded', () => {
    const hoje = new Date();
    const dataFormatada = hoje.toLocaleDateString('pt-BR', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric'
    });
    
    document.getElementById('data-atual').textContent = dataFormatada;
    
    // Verificar se é uma data especial
    const dataAtual = hoje.toISOString().split('T')[0];
    const dataAmistoso = '2025-07-05';
    const dataOficial = '2025-07-12';
    
    if (dataAtual === dataAmistoso) {
        document.querySelector('a[href="/modo/amistoso"]').classList.add('ring-4', 'ring-green-400', 'animate-pulse');
        document.querySelector('a[href="/modo/amistoso"] .text-green-300').innerHTML = '🎯 HOJE É O DIA!';
    } else if (dataAtual === dataOficial) {
        document.querySelector('a[href="/modo/oficial"]').classList.add('ring-4', 'ring-red-400', 'animate-pulse');
        document.querySelector('a[href="/modo/oficial"] .text-red-300').innerHTML = '🎯 HOJE É O DIA!';
    }
});
</script>
{% endblock %}
