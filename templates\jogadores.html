{% extends "base.html" %}

{% block title %}Jogadores - Torneio BAGREMON{% endblock %}

{% block content %}
<div class="space-y-6">
    <!-- Header -->
    <div class="flex justify-between items-center">
        <div>
            <h2 class="text-4xl font-bold text-white mb-2">
                <i class="fas fa-users text-blue-400 mr-3"></i>
                Gerenciar Jogadores
            </h2>
            <p class="text-white/80 text-lg">Cadastre e gerencie os participantes do Torneio BAGREMON</p>
        </div>
        
        <button onclick="abrirModalJogador()" 
                class="bg-green-500 hover:bg-green-600 text-white font-bold py-3 px-6 rounded-lg transition-colors">
            <i class="fas fa-plus mr-2"></i>
            Novo Jogador
        </button>
    </div>

    <!-- Lista de Jogadores -->
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6" id="lista-jogadores">
        <!-- <PERSON><PERSON> preenchido via JavaScript -->
    </div>
</div>

<!-- Modal para Cadastro/Edição de Jogador -->
<div id="modal-jogador" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-md w-full p-6">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-800" id="modal-titulo">Novo Jogador</h3>
                <button onclick="fecharModalJogador()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <form id="form-jogador" class="space-y-4">
                <input type="hidden" id="jogador-id">
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Nome Real</label>
                    <input type="text" id="nome-real" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Nickname</label>
                    <input type="text" id="nickname" required
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tipo Primário</label>
                        <select id="tipo-primario" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Selecione...</option>
                            {% for tipo in tipos %}
                            <option value="{{ tipo }}">{{ tipo }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tipo Secundário</label>
                        <select id="tipo-secundario"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Nenhum</option>
                            {% for tipo in tipos %}
                            <option value="{{ tipo }}">{{ tipo }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Foto de Perfil</label>
                    <div class="space-y-3">
                        <!-- Upload de arquivo -->
                        <div>
                            <input type="file" id="foto-arquivo" accept="image/*"
                                   class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                                   onchange="previewImagemJogador(this)">
                            <p class="text-xs text-gray-500 mt-1">Selecione uma imagem do seu computador (PNG, JPG, GIF)</p>
                        </div>

                        <!-- Preview da imagem -->
                        <div id="preview-container" class="hidden">
                            <img id="preview-imagem" class="h-20 w-20 rounded-full object-cover border-2 border-gray-300" alt="Preview">
                            <button type="button" onclick="removerImagemPreview()"
                                    class="ml-2 text-red-500 hover:text-red-700 text-sm">
                                <i class="fas fa-times"></i> Remover
                            </button>
                        </div>

                        <!-- Campo oculto para armazenar o caminho da imagem -->
                        <input type="hidden" id="foto-perfil">
                    </div>
                </div>
                
                <div class="flex justify-end space-x-3 pt-4">
                    <button type="button" onclick="fecharModalJogador()"
                            class="px-4 py-2 text-gray-600 border border-gray-300 rounded-md hover:bg-gray-50">
                        Cancelar
                    </button>
                    <button type="submit"
                            class="px-4 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600">
                        Salvar
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<!-- Modal para Gerenciar Pokémon -->
<div id="modal-pokemon" class="fixed inset-0 bg-black/50 backdrop-blur-sm hidden z-50">
    <div class="flex items-center justify-center min-h-screen p-4">
        <div class="bg-white rounded-lg max-w-2xl w-full p-6 max-h-[90vh] overflow-y-auto">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-xl font-bold text-gray-800" id="modal-pokemon-titulo">Gerenciar Pokémon</h3>
                <button onclick="fecharModalPokemon()" class="text-gray-500 hover:text-gray-700">
                    <i class="fas fa-times"></i>
                </button>
            </div>
            
            <!-- Formulário para adicionar Pokémon -->
            <form id="form-pokemon" class="space-y-4 mb-6 p-4 bg-gray-50 rounded-lg">
                <h4 class="font-semibold text-gray-800">Adicionar Novo Pokémon</h4>
                <input type="hidden" id="pokemon-jogador-id">
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Nome do Pokémon</label>
                        <input type="text" id="pokemon-nome" required
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Apelido</label>
                        <input type="text" id="pokemon-apelido"
                               class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                    </div>
                </div>
                
                <div class="grid grid-cols-2 gap-4">
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tipo Primário</label>
                        <select id="pokemon-tipo-primario" required
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Selecione...</option>
                            {% for tipo in tipos %}
                            <option value="{{ tipo }}">{{ tipo }}</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div>
                        <label class="block text-sm font-medium text-gray-700 mb-1">Tipo Secundário</label>
                        <select id="pokemon-tipo-secundario"
                                class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500">
                            <option value="">Nenhum</option>
                            {% for tipo in tipos %}
                            <option value="{{ tipo }}">{{ tipo }}</option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                
                <div>
                    <label class="block text-sm font-medium text-gray-700 mb-1">Imagem (URL)</label>
                    <input type="url" id="pokemon-imagem"
                           class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
                           placeholder="https://exemplo.com/pokemon.png">
                </div>
                
                <div class="flex items-center">
                    <input type="checkbox" id="pokemon-credenciado" class="mr-2">
                    <label for="pokemon-credenciado" class="text-sm text-gray-700">Credenciado para as finais</label>
                </div>
                
                <button type="submit" class="w-full bg-green-500 text-white py-2 rounded-md hover:bg-green-600">
                    <i class="fas fa-plus mr-2"></i>
                    Adicionar Pokémon
                </button>
            </form>
            
            <!-- Lista de Pokémon do jogador -->
            <div>
                <h4 class="font-semibold text-gray-800 mb-3">Pokémon Cadastrados</h4>
                <div id="lista-pokemon-jogador" class="space-y-2">
                    <!-- Será preenchido via JavaScript -->
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block scripts %}
<script>
let jogadorAtual = null;

// Função para carregar lista de jogadores
async function carregarJogadores() {
    try {
        const response = await fetch('/api/jogadores');
        const jogadores = await response.json();
        
        const lista = document.getElementById('lista-jogadores');
        lista.innerHTML = '';
        
        jogadores.forEach(jogador => {
            const card = document.createElement('div');
            card.className = 'bg-white/10 backdrop-blur-md rounded-lg p-6 card-hover';
            card.innerHTML = `
                <div class="text-center mb-4">
                    <img class="h-20 w-20 rounded-full mx-auto object-cover border-2 border-white/20 mb-3" 
                         src="${jogador.foto_perfil || '/static/uploads/default-avatar.png'}" 
                         alt="${jogador.nickname}">
                    <h3 class="text-white font-bold text-lg">${jogador.nickname}</h3>
                    <p class="text-white/60">${jogador.nome_real}</p>
                </div>
                
                <div class="flex justify-center space-x-2 mb-4">
                    <span class="tipo-badge ${getTipoClass(jogador.tipo_primario)}">${jogador.tipo_primario}</span>
                    ${jogador.tipo_secundario ? `<span class="tipo-badge ${getTipoClass(jogador.tipo_secundario)}">${jogador.tipo_secundario}</span>` : ''}
                </div>
                
                <div class="grid grid-cols-3 gap-2 text-center text-sm mb-4">
                    <div>
                        <div class="text-green-400 font-bold">${jogador.vitorias}</div>
                        <div class="text-white/60">Vitórias</div>
                    </div>
                    <div>
                        <div class="text-red-400 font-bold">${jogador.derrotas}</div>
                        <div class="text-white/60">Derrotas</div>
                    </div>
                    <div>
                        <div class="text-yellow-400 font-bold">${jogador.pontuacao}</div>
                        <div class="text-white/60">Pontos</div>
                    </div>
                </div>
                
                <div class="text-center text-sm text-white/60 mb-4">
                    ${jogador.pokemons.length} Pokémon cadastrados
                </div>
                
                <div class="flex space-x-2">
                    <button onclick="editarJogador(${jogador.id})" 
                            class="flex-1 bg-blue-500 hover:bg-blue-600 text-white py-2 px-3 rounded text-sm">
                        <i class="fas fa-edit mr-1"></i>
                        Editar
                    </button>
                    <button onclick="gerenciarPokemon(${jogador.id})" 
                            class="flex-1 bg-purple-500 hover:bg-purple-600 text-white py-2 px-3 rounded text-sm">
                        <i class="fas fa-paw mr-1"></i>
                        Pokémon
                    </button>
                    <button onclick="excluirJogador(${jogador.id})" 
                            class="bg-red-500 hover:bg-red-600 text-white py-2 px-3 rounded text-sm">
                        <i class="fas fa-trash"></i>
                    </button>
                </div>
            `;
            lista.appendChild(card);
        });
        
    } catch (error) {
        console.error('Erro ao carregar jogadores:', error);
        alert('Erro ao carregar jogadores. Tente novamente.');
    }
}

// Função para abrir modal de jogador
function abrirModalJogador(jogador = null) {
    const modal = document.getElementById('modal-jogador');
    const titulo = document.getElementById('modal-titulo');
    const form = document.getElementById('form-jogador');
    
    if (jogador) {
        titulo.textContent = 'Editar Jogador';
        document.getElementById('jogador-id').value = jogador.id;
        document.getElementById('nome-real').value = jogador.nome_real;
        document.getElementById('nickname').value = jogador.nickname;
        document.getElementById('tipo-primario').value = jogador.tipo_primario;
        document.getElementById('tipo-secundario').value = jogador.tipo_secundario || '';
        document.getElementById('foto-perfil').value = jogador.foto_perfil || '';
    } else {
        titulo.textContent = 'Novo Jogador';
        form.reset();
        document.getElementById('jogador-id').value = '';
    }
    
    modal.classList.remove('hidden');
}

// Função para fechar modal de jogador
function fecharModalJogador() {
    document.getElementById('modal-jogador').classList.add('hidden');
}

// Função para editar jogador
async function editarJogador(id) {
    try {
        const response = await fetch(`/api/jogadores/${id}`);
        const jogador = await response.json();
        abrirModalJogador(jogador);
    } catch (error) {
        console.error('Erro ao carregar jogador:', error);
        alert('Erro ao carregar dados do jogador.');
    }
}

// Função para excluir jogador
async function excluirJogador(id) {
    if (!confirm('Tem certeza que deseja excluir este jogador? Esta ação não pode ser desfeita.')) {
        return;
    }
    
    try {
        const response = await fetch(`/api/jogadores/${id}`, {
            method: 'DELETE'
        });
        
        if (response.ok) {
            alert('Jogador excluído com sucesso!');
            carregarJogadores();
        } else {
            alert('Erro ao excluir jogador.');
        }
    } catch (error) {
        console.error('Erro ao excluir jogador:', error);
        alert('Erro ao excluir jogador.');
    }
}

// Event listener para o formulário de jogador
document.getElementById('form-jogador').addEventListener('submit', async (e) => {
    e.preventDefault();

    const id = document.getElementById('jogador-id').value;

    // Fazer upload da imagem se houver
    let caminhoImagem = document.getElementById('foto-perfil').value;
    if (document.getElementById('foto-arquivo').files.length > 0) {
        caminhoImagem = await uploadImagemJogador();
        if (!caminhoImagem) {
            return; // Erro no upload, não continuar
        }
    }

    const data = {
        nome_real: document.getElementById('nome-real').value,
        nickname: document.getElementById('nickname').value,
        tipo_primario: document.getElementById('tipo-primario').value,
        tipo_secundario: document.getElementById('tipo-secundario').value || null,
        foto_perfil: caminhoImagem
    };

    try {
        const url = id ? `/api/jogadores/${id}` : '/api/jogadores';
        const method = id ? 'PUT' : 'POST';

        const response = await fetch(url, {
            method: method,
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            alert(id ? 'Jogador atualizado com sucesso!' : 'Jogador cadastrado com sucesso!');
            fecharModalJogador();
            carregarJogadores();
        } else {
            const error = await response.json();
            alert(error.error || 'Erro ao salvar jogador.');
        }
    } catch (error) {
        console.error('Erro ao salvar jogador:', error);
        alert('Erro ao salvar jogador.');
    }
});

// Função para gerenciar Pokémon
async function gerenciarPokemon(jogadorId) {
    try {
        const response = await fetch(`/api/jogadores/${jogadorId}`);
        const jogador = await response.json();

        jogadorAtual = jogador;
        document.getElementById('modal-pokemon-titulo').textContent = `Pokémon de ${jogador.nickname}`;
        document.getElementById('pokemon-jogador-id').value = jogadorId;

        await carregarPokemonJogador(jogadorId);
        document.getElementById('modal-pokemon').classList.remove('hidden');
    } catch (error) {
        console.error('Erro ao carregar jogador:', error);
        alert('Erro ao carregar dados do jogador.');
    }
}

// Função para fechar modal de Pokémon
function fecharModalPokemon() {
    document.getElementById('modal-pokemon').classList.add('hidden');
    document.getElementById('form-pokemon').reset();
}

// Função para carregar Pokémon do jogador
async function carregarPokemonJogador(jogadorId) {
    try {
        const response = await fetch(`/api/pokemons?jogador_id=${jogadorId}`);
        const pokemons = await response.json();

        const lista = document.getElementById('lista-pokemon-jogador');
        lista.innerHTML = '';

        if (pokemons.length === 0) {
            lista.innerHTML = '<p class="text-gray-500 text-center py-4">Nenhum Pokémon cadastrado</p>';
            return;
        }

        pokemons.forEach(pokemon => {
            const item = document.createElement('div');
            item.className = 'flex items-center justify-between p-3 bg-white border rounded-lg';
            item.innerHTML = `
                <div class="flex items-center space-x-3">
                    <img class="h-12 w-12 rounded object-cover"
                         src="${pokemon.imagem || '/static/uploads/default-pokemon.png'}"
                         alt="${pokemon.nome}">
                    <div>
                        <div class="font-medium text-gray-800">
                            ${pokemon.nome}
                            ${pokemon.apelido ? `"${pokemon.apelido}"` : ''}
                        </div>
                        <div class="flex space-x-1">
                            <span class="tipo-badge ${getTipoClass(pokemon.tipo_primario)}">${pokemon.tipo_primario}</span>
                            ${pokemon.tipo_secundario ? `<span class="tipo-badge ${getTipoClass(pokemon.tipo_secundario)}">${pokemon.tipo_secundario}</span>` : ''}
                        </div>
                        ${pokemon.credenciado_finais ? '<span class="text-xs text-green-600 font-medium">✓ Credenciado para finais</span>' : ''}
                    </div>
                </div>
                <button onclick="excluirPokemon(${pokemon.id})"
                        class="text-red-500 hover:text-red-700">
                    <i class="fas fa-trash"></i>
                </button>
            `;
            lista.appendChild(item);
        });

    } catch (error) {
        console.error('Erro ao carregar Pokémon:', error);
        alert('Erro ao carregar Pokémon do jogador.');
    }
}

// Função para excluir Pokémon
async function excluirPokemon(pokemonId) {
    if (!confirm('Tem certeza que deseja excluir este Pokémon?')) {
        return;
    }

    try {
        const response = await fetch(`/api/pokemons/${pokemonId}`, {
            method: 'DELETE'
        });

        if (response.ok) {
            alert('Pokémon excluído com sucesso!');
            carregarPokemonJogador(document.getElementById('pokemon-jogador-id').value);
            carregarJogadores(); // Atualizar contagem na lista principal
        } else {
            alert('Erro ao excluir Pokémon.');
        }
    } catch (error) {
        console.error('Erro ao excluir Pokémon:', error);
        alert('Erro ao excluir Pokémon.');
    }
}

// Event listener para o formulário de Pokémon
document.getElementById('form-pokemon').addEventListener('submit', async (e) => {
    e.preventDefault();

    const data = {
        nome: document.getElementById('pokemon-nome').value,
        apelido: document.getElementById('pokemon-apelido').value || null,
        tipo_primario: document.getElementById('pokemon-tipo-primario').value,
        tipo_secundario: document.getElementById('pokemon-tipo-secundario').value || null,
        imagem: document.getElementById('pokemon-imagem').value || null,
        credenciado_finais: document.getElementById('pokemon-credenciado').checked,
        jogador_id: document.getElementById('pokemon-jogador-id').value
    };

    try {
        const response = await fetch('/api/pokemons', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });

        if (response.ok) {
            alert('Pokémon cadastrado com sucesso!');
            document.getElementById('form-pokemon').reset();
            carregarPokemonJogador(data.jogador_id);
            carregarJogadores(); // Atualizar contagem na lista principal
        } else {
            const error = await response.json();
            alert(error.error || 'Erro ao cadastrar Pokémon.');
        }
    } catch (error) {
        console.error('Erro ao cadastrar Pokémon:', error);
        alert('Erro ao cadastrar Pokémon.');
    }
});

// Função para preview da imagem do jogador
function previewImagemJogador(input) {
    if (input.files && input.files[0]) {
        const file = input.files[0];

        // Verificar se é uma imagem
        if (!file.type.startsWith('image/')) {
            alert('Por favor, selecione apenas arquivos de imagem.');
            input.value = '';
            return;
        }

        // Verificar tamanho (máximo 5MB)
        if (file.size > 5 * 1024 * 1024) {
            alert('A imagem deve ter no máximo 5MB.');
            input.value = '';
            return;
        }

        const reader = new FileReader();
        reader.onload = function(e) {
            document.getElementById('preview-imagem').src = e.target.result;
            document.getElementById('preview-container').classList.remove('hidden');
        };
        reader.readAsDataURL(file);
    }
}

// Função para remover preview da imagem
function removerImagemPreview() {
    document.getElementById('foto-arquivo').value = '';
    document.getElementById('preview-container').classList.add('hidden');
    document.getElementById('foto-perfil').value = '';
}

// Função para fazer upload da imagem
async function uploadImagemJogador() {
    const fileInput = document.getElementById('foto-arquivo');
    if (!fileInput.files || !fileInput.files[0]) {
        return null;
    }

    const formData = new FormData();
    formData.append('imagem', fileInput.files[0]);

    try {
        const response = await fetch('/api/upload-imagem', {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const result = await response.json();
            return result.caminho;
        } else {
            const error = await response.json();
            throw new Error(error.error || 'Erro ao fazer upload da imagem');
        }
    } catch (error) {
        console.error('Erro no upload:', error);
        alert('Erro ao fazer upload da imagem: ' + error.message);
        return null;
    }
}

// Carregar jogadores ao carregar a página
document.addEventListener('DOMContentLoaded', carregarJogadores);
</script>
{% endblock %}
