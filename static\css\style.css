/* Estilos customizados para o Campeonato Pokémon VGC */

/* Animações */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

@keyframes glow {
    0% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
    50% { box-shadow: 0 0 20px rgba(255, 215, 0, 0.8); }
    100% { box-shadow: 0 0 5px rgba(255, 215, 0, 0.5); }
}

/* Classes de animação */
.fade-in {
    animation: fadeIn 0.6s ease-out;
}

.slide-in {
    animation: slideIn 0.5s ease-out;
}

.bounce-in {
    animation: bounce 1s ease-out;
}

.pulse-hover:hover {
    animation: pulse 0.3s ease-in-out;
}

.glow-effect {
    animation: glow 2s ease-in-out infinite;
}

/* Efeitos de hover melhorados */
.card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-hover:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Botões com efeitos especiais */
.btn-pokemon {
    position: relative;
    overflow: hidden;
    transition: all 0.3s ease;
}

.btn-pokemon::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-pokemon:hover::before {
    left: 100%;
}

/* Efeitos de loading */
.loading {
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 20px;
    height: 20px;
    margin: -10px 0 0 -10px;
    border: 2px solid #ffffff;
    border-radius: 50%;
    border-top-color: transparent;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* Efeitos de tipos Pokémon com gradientes */
.tipo-normal { 
    background: linear-gradient(135deg, #A8A878, #C5C5A3); 
    box-shadow: 0 4px 8px rgba(168, 168, 120, 0.3);
}

.tipo-fire { 
    background: linear-gradient(135deg, #F08030, #FF9955); 
    box-shadow: 0 4px 8px rgba(240, 128, 48, 0.3);
}

.tipo-water { 
    background: linear-gradient(135deg, #6890F0, #85A8FF); 
    box-shadow: 0 4px 8px rgba(104, 144, 240, 0.3);
}

.tipo-electric { 
    background: linear-gradient(135deg, #F8D030, #FFE055); 
    box-shadow: 0 4px 8px rgba(248, 208, 48, 0.3);
}

.tipo-grass { 
    background: linear-gradient(135deg, #78C850, #95D975); 
    box-shadow: 0 4px 8px rgba(120, 200, 80, 0.3);
}

.tipo-ice { 
    background: linear-gradient(135deg, #98D8D8, #B3E5E5); 
    box-shadow: 0 4px 8px rgba(152, 216, 216, 0.3);
}

.tipo-fighting { 
    background: linear-gradient(135deg, #C03028, #D5554D); 
    box-shadow: 0 4px 8px rgba(192, 48, 40, 0.3);
}

.tipo-poison { 
    background: linear-gradient(135deg, #A040A0, #B565B5); 
    box-shadow: 0 4px 8px rgba(160, 64, 160, 0.3);
}

.tipo-ground { 
    background: linear-gradient(135deg, #E0C068, #E8D08D); 
    box-shadow: 0 4px 8px rgba(224, 192, 104, 0.3);
}

.tipo-flying { 
    background: linear-gradient(135deg, #A890F0, #BDA8FF); 
    box-shadow: 0 4px 8px rgba(168, 144, 240, 0.3);
}

.tipo-psychic { 
    background: linear-gradient(135deg, #F85888, #FF7DA3); 
    box-shadow: 0 4px 8px rgba(248, 88, 136, 0.3);
}

.tipo-bug { 
    background: linear-gradient(135deg, #A8B820, #BDC945); 
    box-shadow: 0 4px 8px rgba(168, 184, 32, 0.3);
}

.tipo-rock { 
    background: linear-gradient(135deg, #B8A038, #C9B15D); 
    box-shadow: 0 4px 8px rgba(184, 160, 56, 0.3);
}

.tipo-ghost { 
    background: linear-gradient(135deg, #705898, #8573AD); 
    box-shadow: 0 4px 8px rgba(112, 88, 152, 0.3);
}

.tipo-dragon { 
    background: linear-gradient(135deg, #7038F8, #8D5DFF); 
    box-shadow: 0 4px 8px rgba(112, 56, 248, 0.3);
}

.tipo-dark { 
    background: linear-gradient(135deg, #705848, #8B735D); 
    box-shadow: 0 4px 8px rgba(112, 88, 72, 0.3);
}

.tipo-steel { 
    background: linear-gradient(135deg, #B8B8D0, #C9C9DD); 
    box-shadow: 0 4px 8px rgba(184, 184, 208, 0.3);
}

.tipo-fairy { 
    background: linear-gradient(135deg, #EE99AC, #F2B4C1); 
    box-shadow: 0 4px 8px rgba(238, 153, 172, 0.3);
}

/* Efeitos especiais para posições */
.posicao-1 {
    background: linear-gradient(135deg, #FFD700, #FFA500);
    color: #000;
    font-weight: bold;
    animation: glow 2s ease-in-out infinite;
}

.posicao-ultimo {
    background: linear-gradient(135deg, #FF6B6B, #EE5A52);
    color: white;
    animation: pulse 2s ease-in-out infinite;
}

.posicao-normal {
    background: linear-gradient(135deg, #74b9ff, #0984e3);
    color: white;
}

/* Efeitos de modal */
.modal-backdrop {
    backdrop-filter: blur(8px);
    background: rgba(0, 0, 0, 0.6);
}

.modal-content {
    animation: fadeIn 0.3s ease-out;
}

/* Scrollbar customizada */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 255, 255, 0.5);
}

/* Efeitos de foco melhorados */
input:focus, select:focus, textarea:focus {
    outline: none;
    ring: 2px;
    ring-color: #3B82F6;
    border-color: #3B82F6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Transições suaves para todos os elementos interativos */
button, input, select, textarea, .card-hover, .btn-pokemon {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Efeitos de hover para botões */
button:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.25);
}

button:active {
    transform: translateY(0);
}

/* Responsividade melhorada */
@media (max-width: 768px) {
    .card-hover:hover {
        transform: translateY(-4px) scale(1.01);
    }
    
    .pokemon-title {
        font-size: 1.5rem;
    }
    
    .tipo-badge {
        font-size: 0.65rem;
        padding: 2px 8px;
    }
}

/* Efeitos especiais para elementos importantes */
.destaque-campeao {
    position: relative;
    overflow: hidden;
}

.destaque-campeao::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: conic-gradient(from 0deg, transparent, rgba(255, 215, 0, 0.3), transparent);
    animation: rotate 3s linear infinite;
}

@keyframes rotate {
    to { transform: rotate(360deg); }
}

.destaque-campeao > * {
    position: relative;
    z-index: 1;
}

/* Indicadores visuais */
.status-indicator {
    position: relative;
}

.status-indicator::after {
    content: '';
    position: absolute;
    top: -2px;
    right: -2px;
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #10B981;
    animation: pulse 2s ease-in-out infinite;
}

/* Efeitos de texto */
.text-glow {
    text-shadow: 0 0 10px rgba(255, 255, 255, 0.5);
}

.text-pokemon {
    background: linear-gradient(45deg, #FFD700, #FFA500, #FF6B6B, #4ECDC4, #45B7D1);
    background-size: 300% 300%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradient 3s ease infinite;
}

@keyframes gradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}
