<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{% block title %}Campeonato Pokémon VGC{% endblock %}</title>
    
    <!-- TailwindCSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <!-- Font Awesome para ícones -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Google Fonts -->
    <link href="https://fonts.googleapis.com/css2?family=Orbitron:wght@400;700;900&family=Roboto:wght@300;400;500;700&display=swap" rel="stylesheet">
    
    <!-- CSS customizado -->
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
    
    <style>
        body {
            font-family: 'Roboto', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .pokemon-title {
            font-family: 'Orbitron', monospace;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
        }
        
        .card-hover {
            transition: all 0.3s ease;
        }
        
        .card-hover:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
        }
        
        .tipo-badge {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.75rem;
            font-weight: 600;
            text-transform: uppercase;
            color: white;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.5);
        }
        
        /* Cores dos tipos Pokémon */
        .tipo-normal { background: #A8A878; }
        .tipo-fire { background: #F08030; }
        .tipo-water { background: #6890F0; }
        .tipo-electric { background: #F8D030; }
        .tipo-grass { background: #78C850; }
        .tipo-ice { background: #98D8D8; }
        .tipo-fighting { background: #C03028; }
        .tipo-poison { background: #A040A0; }
        .tipo-ground { background: #E0C068; }
        .tipo-flying { background: #A890F0; }
        .tipo-psychic { background: #F85888; }
        .tipo-bug { background: #A8B820; }
        .tipo-rock { background: #B8A038; }
        .tipo-ghost { background: #705898; }
        .tipo-dragon { background: #7038F8; }
        .tipo-dark { background: #705848; }
        .tipo-steel { background: #B8B8D0; }
        .tipo-fairy { background: #EE99AC; }
        
        .posicao-1 {
            background: linear-gradient(135deg, #FFD700, #FFA500);
            color: #000;
            font-weight: bold;
        }
        
        .posicao-ultimo {
            background: linear-gradient(135deg, #FF6B6B, #EE5A52);
            color: white;
        }
        
        .posicao-normal {
            background: linear-gradient(135deg, #74b9ff, #0984e3);
            color: white;
        }
    </style>
</head>
<body class="bg-gradient-to-br from-blue-500 to-purple-600 min-h-screen">
    <!-- Navbar -->
    <nav class="bg-white/10 backdrop-blur-md border-b border-white/20">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="flex justify-between h-16">
                <div class="flex items-center">
                    <h1 class="text-2xl font-bold pokemon-title">
                        <i class="fas fa-trophy mr-2"></i>
                        Campeonato Pokémon VGC
                    </h1>
                </div>
                
                <div class="flex items-center space-x-4">
                    <a href="{{ url_for('index') }}" 
                       class="text-white hover:text-yellow-300 px-3 py-2 rounded-md text-sm font-medium transition-colors
                              {% if request.endpoint == 'index' %}bg-white/20{% endif %}">
                        <i class="fas fa-table mr-1"></i>
                        Classificação
                    </a>
                    <a href="{{ url_for('jogadores') }}" 
                       class="text-white hover:text-yellow-300 px-3 py-2 rounded-md text-sm font-medium transition-colors
                              {% if request.endpoint == 'jogadores' %}bg-white/20{% endif %}">
                        <i class="fas fa-users mr-1"></i>
                        Jogadores
                    </a>
                    <a href="{{ url_for('batalhas') }}" 
                       class="text-white hover:text-yellow-300 px-3 py-2 rounded-md text-sm font-medium transition-colors
                              {% if request.endpoint == 'batalhas' %}bg-white/20{% endif %}">
                        <i class="fas fa-fist-raised mr-1"></i>
                        Batalhas
                    </a>
                    <a href="{{ url_for('repescagem') }}" 
                       class="text-white hover:text-yellow-300 px-3 py-2 rounded-md text-sm font-medium transition-colors
                              {% if request.endpoint == 'repescagem' %}bg-white/20{% endif %}">
                        <i class="fas fa-sitemap mr-1"></i>
                        Repescagem
                    </a>
                </div>
            </div>
        </div>
    </nav>

    <!-- Conteúdo principal -->
    <main class="max-w-7xl mx-auto py-6 px-4 sm:px-6 lg:px-8">
        {% block content %}{% endblock %}
    </main>

    <!-- Scripts -->
    <script src="{{ url_for('static', filename='js/app.js') }}"></script>
    {% block scripts %}{% endblock %}
</body>
</html>
